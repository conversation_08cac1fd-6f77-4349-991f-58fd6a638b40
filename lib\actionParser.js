class ActionParser {
  constructor() {
    this.supportedActions = [
      'CREATE_FILE',
      'UPDATE_FILE',
      'DELETE_FILE',
      'CREATE_DIRECTORY',
      'RUN_COMMAND',
      'INSTALL_PACKAGE'
    ];
  }

  // Parse actions from AI response
  parseActions(response) {
    const actions = [];
    
    // Try to extract JSON actions first
    const jsonActions = this.extractJsonActions(response);
    if (jsonActions.length > 0) {
      actions.push(...jsonActions);
    }

    // Try to extract text-based actions
    const textActions = this.extractTextActions(response);
    if (textActions.length > 0) {
      actions.push(...textActions);
    }

    // Validate and clean actions
    return actions
      .filter(action => this.validateAction(action))
      .map(action => this.normalizeAction(action));
  }

  // Extract JSON-formatted actions
  extractJsonActions(response) {
    const actions = [];
    
    // Look for JSON code blocks
    const jsonBlocks = response.match(/```json\s*([\s\S]*?)\s*```/gi);
    
    if (jsonBlocks) {
      for (const block of jsonBlocks) {
        try {
          const jsonContent = block.replace(/```json\s*|\s*```/gi, '').trim();
          const parsed = JSON.parse(jsonContent);
          
          if (parsed.actions && Array.isArray(parsed.actions)) {
            actions.push(...parsed.actions);
          } else if (parsed.type && parsed.payload) {
            // Single action format
            actions.push(parsed);
          }
        } catch (error) {
          console.warn('Failed to parse JSON action block:', error.message);
        }
      }
    }

    // Look for inline JSON
    const inlineJsonRegex = /\{[\s\S]*?"actions"[\s\S]*?\}/g;
    const inlineMatches = response.match(inlineJsonRegex);
    
    if (inlineMatches) {
      for (const match of inlineMatches) {
        try {
          const parsed = JSON.parse(match);
          if (parsed.actions && Array.isArray(parsed.actions)) {
            actions.push(...parsed.actions);
          }
        } catch (error) {
          // Ignore invalid JSON
        }
      }
    }

    return actions;
  }

  // Extract text-based action patterns
  extractTextActions(response) {
    const actions = [];
    const lines = response.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Pattern: CREATE FILE: path
      if (line.match(/^CREATE FILE:\s*(.+)$/i)) {
        const path = line.match(/^CREATE FILE:\s*(.+)$/i)[1];
        const content = this.extractCodeBlock(lines, i + 1);
        if (content) {
          actions.push({
            type: 'CREATE_FILE',
            payload: { path, content }
          });
        }
      }

      // Pattern: UPDATE FILE: path
      if (line.match(/^UPDATE FILE:\s*(.+)$/i)) {
        const path = line.match(/^UPDATE FILE:\s*(.+)$/i)[1];
        const content = this.extractCodeBlock(lines, i + 1);
        if (content) {
          actions.push({
            type: 'UPDATE_FILE',
            payload: { path, content }
          });
        }
      }

      // Pattern: DELETE FILE: path
      if (line.match(/^DELETE FILE:\s*(.+)$/i)) {
        const path = line.match(/^DELETE FILE:\s*(.+)$/i)[1];
        actions.push({
          type: 'DELETE_FILE',
          payload: { path }
        });
      }

      // Pattern: RUN COMMAND: command
      if (line.match(/^RUN COMMAND:\s*(.+)$/i)) {
        const command = line.match(/^RUN COMMAND:\s*(.+)$/i)[1];
        actions.push({
          type: 'RUN_COMMAND',
          payload: { command }
        });
      }

      // Pattern: npm install package
      if (line.match(/^npm install\s+(.+)$/i)) {
        const packages = line.match(/^npm install\s+(.+)$/i)[1];
        actions.push({
          type: 'RUN_COMMAND',
          payload: { command: `npm install ${packages}` }
        });
      }
    }

    return actions;
  }

  // Extract code block following a line
  extractCodeBlock(lines, startIndex) {
    let content = '';
    let inCodeBlock = false;
    let codeBlockType = '';

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i];

      // Check for code block start
      if (line.trim().startsWith('```')) {
        if (!inCodeBlock) {
          inCodeBlock = true;
          codeBlockType = line.trim().substring(3);
          continue;
        } else {
          // End of code block
          break;
        }
      }

      if (inCodeBlock) {
        content += line + '\n';
      } else if (line.trim() === '') {
        // Skip empty lines before code block
        continue;
      } else {
        // No code block found
        break;
      }
    }

    return content.trim();
  }

  // Validate action structure
  validateAction(action) {
    if (!action || typeof action !== 'object') {
      return false;
    }

    if (!action.type || !this.supportedActions.includes(action.type)) {
      return false;
    }

    if (!action.payload || typeof action.payload !== 'object') {
      return false;
    }

    // Validate specific action types
    switch (action.type) {
      case 'CREATE_FILE':
      case 'UPDATE_FILE':
        return action.payload.path && typeof action.payload.content === 'string';
      
      case 'DELETE_FILE':
      case 'CREATE_DIRECTORY':
        return action.payload.path;
      
      case 'RUN_COMMAND':
        return action.payload.command && typeof action.payload.command === 'string';
      
      default:
        return false;
    }
  }

  // Normalize action format
  normalizeAction(action) {
    const normalized = {
      type: action.type,
      payload: { ...action.payload }
    };

    // Clean up file paths
    if (normalized.payload.path) {
      normalized.payload.path = this.normalizePath(normalized.payload.path);
    }

    // Clean up commands
    if (normalized.payload.command) {
      normalized.payload.command = normalized.payload.command.trim();
    }

    // Clean up content
    if (normalized.payload.content) {
      normalized.payload.content = this.normalizeContent(normalized.payload.content);
    }

    return normalized;
  }

  // Normalize file paths
  normalizePath(filePath) {
    return filePath
      .trim()
      .replace(/^['"]+|['"]+$/g, '') // Remove quotes
      .replace(/\\/g, '/') // Convert backslashes to forward slashes
      .replace(/^\/+/, ''); // Remove leading slashes
  }

  // Normalize file content
  normalizeContent(content) {
    return content
      .replace(/\\n/g, '\n') // Convert escaped newlines
      .replace(/\\t/g, '\t') // Convert escaped tabs
      .replace(/\\"/g, '"') // Convert escaped quotes
      .trim();
  }

  // Extract file operations from natural language
  extractImplicitActions(response) {
    const actions = [];
    const sentences = response.split(/[.!?]+/);

    for (const sentence of sentences) {
      const trimmed = sentence.trim().toLowerCase();

      // Look for creation patterns
      if (trimmed.includes('create') && (trimmed.includes('file') || trimmed.includes('component'))) {
        const pathMatch = trimmed.match(/create.*?(?:file|component).*?(?:called|named|at)\s+([^\s,]+)/);
        if (pathMatch) {
          // This would need more sophisticated parsing
          // For now, we rely on explicit action formats
        }
      }

      // Look for installation patterns
      if (trimmed.includes('install') && (trimmed.includes('npm') || trimmed.includes('package'))) {
        const packageMatch = trimmed.match(/install\s+([a-z-@/]+)/);
        if (packageMatch) {
          actions.push({
            type: 'RUN_COMMAND',
            payload: { command: `npm install ${packageMatch[1]}` }
          });
        }
      }
    }

    return actions;
  }

  // Parse actions with confidence scoring
  parseActionsWithConfidence(response) {
    const actions = this.parseActions(response);
    
    return actions.map(action => ({
      ...action,
      confidence: this.calculateActionConfidence(action, response)
    }));
  }

  // Calculate confidence score for an action
  calculateActionConfidence(action, response) {
    let confidence = 0.5; // Base confidence

    // Higher confidence for JSON-formatted actions
    if (response.includes('```json')) {
      confidence += 0.3;
    }

    // Higher confidence for explicit action keywords
    const actionKeywords = {
      'CREATE_FILE': ['create', 'new file', 'add file'],
      'UPDATE_FILE': ['update', 'modify', 'change', 'edit'],
      'DELETE_FILE': ['delete', 'remove', 'rm'],
      'RUN_COMMAND': ['run', 'execute', 'install']
    };

    const keywords = actionKeywords[action.type] || [];
    const responseText = response.toLowerCase();
    
    for (const keyword of keywords) {
      if (responseText.includes(keyword)) {
        confidence += 0.1;
      }
    }

    // Cap confidence at 1.0
    return Math.min(confidence, 1.0);
  }

  // Group related actions
  groupActions(actions) {
    const groups = [];
    let currentGroup = [];

    for (const action of actions) {
      if (currentGroup.length === 0) {
        currentGroup.push(action);
      } else {
        const lastAction = currentGroup[currentGroup.length - 1];
        
        // Group file operations on the same file
        if (this.areActionsRelated(lastAction, action)) {
          currentGroup.push(action);
        } else {
          groups.push([...currentGroup]);
          currentGroup = [action];
        }
      }
    }

    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }

    return groups;
  }

  // Check if two actions are related
  areActionsRelated(action1, action2) {
    // Same file operations
    if (action1.payload.path && action2.payload.path) {
      return action1.payload.path === action2.payload.path;
    }

    // Related commands
    if (action1.type === 'RUN_COMMAND' && action2.type === 'RUN_COMMAND') {
      const cmd1 = action1.payload.command.split(' ')[0];
      const cmd2 = action2.payload.command.split(' ')[0];
      return cmd1 === cmd2;
    }

    return false;
  }
}

module.exports = ActionParser;
