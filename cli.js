#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const ora = require('ora');
const open = require('open');
const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');
const { v4: uuidv4 } = require('uuid');

const program = new Command();
const CONFIG_DIR = path.join(process.cwd(), '.chatproj');
const PID_FILE = path.join(CONFIG_DIR, 'chatproj.pid');
const CONFIG_FILE = path.join(CONFIG_DIR, 'config.json');

// Utility functions
function ensureConfigDir() {
  if (!fs.existsSync(CONFIG_DIR)) {
    fs.mkdirSync(CONFIG_DIR, { recursive: true });
  }
}

function findAvailablePort(startPort = 3000) {
  return new Promise((resolve) => {
    const net = require('net');
    const server = net.createServer();
    
    server.listen(startPort, () => {
      const port = server.address().port;
      server.close(() => resolve(port));
    });
    
    server.on('error', () => {
      resolve(findAvailablePort(startPort + 1));
    });
  });
}

function isProcessRunning(pid) {
  try {
    process.kill(pid, 0);
    return true;
  } catch (e) {
    return false;
  }
}

function getRunningInstance() {
  if (!fs.existsSync(PID_FILE)) return null;
  
  try {
    const data = JSON.parse(fs.readFileSync(PID_FILE, 'utf8'));
    if (isProcessRunning(data.pid)) {
      return data;
    } else {
      fs.unlinkSync(PID_FILE);
      return null;
    }
  } catch (e) {
    return null;
  }
}

function saveInstanceInfo(pid, port, projectPath) {
  ensureConfigDir();
  const instanceInfo = {
    pid,
    port,
    projectPath,
    startTime: new Date().toISOString(),
    id: uuidv4()
  };
  fs.writeFileSync(PID_FILE, JSON.stringify(instanceInfo, null, 2));
  return instanceInfo;
}

function validateProject() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.log(chalk.yellow('⚠️  No package.json found. This might not be a Node.js project.'));
    console.log(chalk.gray('   You can still use ChatProj, but some features may be limited.'));
    return false;
  }
  return true;
}

// Commands
program
  .name('chatproj')
  .description('Universal AI coding assistant')
  .version('1.0.0');

program
  .command('launch')
  .description('Launch the AI coding assistant')
  .option('-p, --port <port>', 'specify port (default: auto-detect)')
  .option('--no-browser', 'don\'t open browser automatically')
  .action(async (options) => {
    const spinner = ora('Starting ChatProj...').start();
    
    try {
      // Check if already running
      const running = getRunningInstance();
      if (running) {
        spinner.succeed(`ChatProj is already running on port ${running.port}`);
        if (options.browser !== false) {
          console.log(chalk.blue(`🌐 Opening http://localhost:${running.port}`));
          await open(`http://localhost:${running.port}`);
        }
        return;
      }

      // Validate project
      validateProject();

      // Find available port
      const port = options.port ? parseInt(options.port) : await findAvailablePort();
      spinner.text = `Starting server on port ${port}...`;

      // Start server
      const serverPath = path.join(__dirname, 'server.js');
      const serverProcess = spawn('node', [serverPath], {
        env: { ...process.env, PORT: port, PROJECT_PATH: process.cwd() },
        detached: true,
        stdio: 'ignore'
      });

      serverProcess.unref();

      // Save instance info
      const instanceInfo = saveInstanceInfo(serverProcess.pid, port, process.cwd());
      
      // Wait a moment for server to start
      await new Promise(resolve => setTimeout(resolve, 2000));

      spinner.succeed(`ChatProj launched successfully!`);
      console.log(chalk.green(`🚀 Server running on http://localhost:${port}`));
      console.log(chalk.gray(`   Project: ${process.cwd()}`));
      console.log(chalk.gray(`   PID: ${serverProcess.pid}`));

      // Open browser
      if (options.browser !== false) {
        console.log(chalk.blue('🌐 Opening browser...'));
        await open(`http://localhost:${port}`);
      }

      console.log(chalk.yellow('\n💡 Use "chatproj stop" to stop the server'));
      
    } catch (error) {
      spinner.fail('Failed to launch ChatProj');
      console.error(chalk.red(error.message));
      process.exit(1);
    }
  });

program
  .command('stop')
  .description('Stop the running ChatProj instance')
  .action(() => {
    const running = getRunningInstance();
    
    if (!running) {
      console.log(chalk.yellow('No ChatProj instance is currently running'));
      return;
    }

    try {
      process.kill(running.pid, 'SIGTERM');
      fs.unlinkSync(PID_FILE);
      console.log(chalk.green('✅ ChatProj stopped successfully'));
    } catch (error) {
      console.error(chalk.red('Failed to stop ChatProj:', error.message));
      // Try to clean up PID file anyway
      if (fs.existsSync(PID_FILE)) {
        fs.unlinkSync(PID_FILE);
      }
    }
  });

program
  .command('status')
  .description('Check ChatProj status')
  .action(() => {
    const running = getRunningInstance();
    
    if (running) {
      console.log(chalk.green('✅ ChatProj is running'));
      console.log(chalk.gray(`   URL: http://localhost:${running.port}`));
      console.log(chalk.gray(`   Project: ${running.projectPath}`));
      console.log(chalk.gray(`   PID: ${running.pid}`));
      console.log(chalk.gray(`   Started: ${new Date(running.startTime).toLocaleString()}`));
    } else {
      console.log(chalk.yellow('❌ ChatProj is not running'));
    }
  });

program
  .command('init')
  .description('Initialize ChatProj configuration for this project')
  .action(() => {
    ensureConfigDir();
    
    const config = {
      projectName: path.basename(process.cwd()),
      createdAt: new Date().toISOString(),
      settings: {
        autoLaunch: false,
        preferredPort: 3000,
        openBrowser: true
      }
    };
    
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
    
    // Create .env if it doesn't exist
    const envPath = path.join(process.cwd(), '.env');
    if (!fs.existsSync(envPath)) {
      const envExample = path.join(__dirname, '.env.example');
      if (fs.existsSync(envExample)) {
        fs.copyFileSync(envExample, envPath);
        console.log(chalk.green('✅ Created .env file'));
        console.log(chalk.yellow('⚠️  Please add your OPENAI_API_KEY to .env'));
      }
    }
    
    console.log(chalk.green('✅ ChatProj initialized for this project'));
    console.log(chalk.gray(`   Config: ${CONFIG_FILE}`));
  });

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Unexpected error:', error.message));
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error(chalk.red('Unhandled promise rejection:', reason));
  process.exit(1);
});

program.parse();
