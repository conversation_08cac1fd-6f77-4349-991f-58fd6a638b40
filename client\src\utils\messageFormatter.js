// Simple markdown-like formatter for chat messages
export function formatMessage(content) {
  if (!content) return '';

  let formatted = content;

  // Code blocks with syntax highlighting
  formatted = formatted.replace(
    /```(\w+)?\n([\s\S]*?)```/g,
    (match, language, code) => {
      const lang = language || 'text';
      return `<pre class="bg-gray-800 border border-gray-600 rounded-lg p-4 overflow-x-auto my-3"><code class="language-${lang} text-sm font-mono">${escapeHtml(code.trim())}</code></pre>`;
    }
  );

  // Inline code
  formatted = formatted.replace(
    /`([^`]+)`/g,
    '<code class="bg-gray-700 px-2 py-1 rounded text-sm font-mono">$1</code>'
  );

  // Bold text
  formatted = formatted.replace(
    /\*\*(.*?)\*\*/g,
    '<strong class="font-semibold">$1</strong>'
  );

  // Italic text
  formatted = formatted.replace(
    /\*(.*?)\*/g,
    '<em class="italic">$1</em>'
  );

  // Links
  formatted = formatted.replace(
    /\[([^\]]+)\]\(([^)]+)\)/g,
    '<a href="$2" class="text-blue-400 hover:text-blue-300 underline" target="_blank" rel="noopener noreferrer">$1</a>'
  );

  // Auto-link URLs
  formatted = formatted.replace(
    /(https?:\/\/[^\s]+)/g,
    '<a href="$1" class="text-blue-400 hover:text-blue-300 underline" target="_blank" rel="noopener noreferrer">$1</a>'
  );

  // Headers
  formatted = formatted.replace(
    /^### (.*$)/gm,
    '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>'
  );
  formatted = formatted.replace(
    /^## (.*$)/gm,
    '<h2 class="text-xl font-semibold mt-4 mb-2">$1</h2>'
  );
  formatted = formatted.replace(
    /^# (.*$)/gm,
    '<h1 class="text-2xl font-bold mt-4 mb-2">$1</h1>'
  );

  // Lists
  formatted = formatted.replace(
    /^- (.*$)/gm,
    '<li class="ml-4">• $1</li>'
  );
  formatted = formatted.replace(
    /^(\d+)\. (.*$)/gm,
    '<li class="ml-4">$1. $2</li>'
  );

  // Wrap consecutive list items
  formatted = formatted.replace(
    /(<li class="ml-4">.*<\/li>\s*)+/g,
    '<ul class="my-2">$&</ul>'
  );

  // Line breaks
  formatted = formatted.replace(/\n/g, '<br>');

  // Clean up extra breaks around code blocks and headers
  formatted = formatted.replace(/<br>\s*(<pre|<h[1-6]|<ul)/g, '$1');
  formatted = formatted.replace(/(<\/pre>|<\/h[1-6]>|<\/ul>)\s*<br>/g, '$1');

  return formatted;
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

// Extract code blocks from message for syntax highlighting
export function extractCodeBlocks(content) {
  const codeBlocks = [];
  const regex = /```(\w+)?\n([\s\S]*?)```/g;
  let match;

  while ((match = regex.exec(content)) !== null) {
    codeBlocks.push({
      language: match[1] || 'text',
      code: match[2].trim(),
      fullMatch: match[0]
    });
  }

  return codeBlocks;
}

// Format file paths for display
export function formatFilePath(path) {
  if (!path) return '';
  
  // Highlight file extensions
  const parts = path.split('/');
  const fileName = parts[parts.length - 1];
  const directory = parts.slice(0, -1).join('/');
  
  if (directory) {
    return `<span class="text-gray-400">${directory}/</span><span class="text-gray-200">${fileName}</span>`;
  }
  
  return `<span class="text-gray-200">${fileName}</span>`;
}

// Format command output
export function formatCommandOutput(output) {
  if (!output) return '';
  
  return `<pre class="bg-gray-900 border border-gray-600 rounded p-3 text-sm font-mono overflow-x-auto">${escapeHtml(output)}</pre>`;
}

// Format JSON for display
export function formatJson(obj) {
  try {
    const formatted = JSON.stringify(obj, null, 2);
    return `<pre class="bg-gray-800 border border-gray-600 rounded p-3 text-sm font-mono overflow-x-auto"><code class="language-json">${escapeHtml(formatted)}</code></pre>`;
  } catch (error) {
    return `<span class="text-red-400">Invalid JSON: ${error.message}</span>`;
  }
}

// Truncate long text with ellipsis
export function truncateText(text, maxLength = 100) {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

// Highlight search terms in text
export function highlightSearchTerms(text, searchTerm) {
  if (!searchTerm || !text) return text;
  
  const regex = new RegExp(`(${escapeRegex(searchTerm)})`, 'gi');
  return text.replace(regex, '<mark class="bg-yellow-400 text-black">$1</mark>');
}

// Escape regex special characters
function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
