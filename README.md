# ChatProj - Universal AI Coding Assistant

A browser-based AI coding assistant that launches from any project directory. Get a ChatGPT-like interface in your browser to code with AI assistance.

## Features

- 🚀 **Universal**: Works with any JavaScript/Node.js project
- 🌐 **Browser-based**: Clean web interface, no IDE dependencies
- ⚡ **Real-time**: Streaming AI responses via WebSocket
- 📁 **File Management**: AI can create, edit, and manage project files
- 🔒 **Secure**: Sandboxed to your project directory
- 🎯 **Simple**: Single command to launch

## Quick Start

```bash
# Install globally
npm install -g chatproj

# Navigate to your project
cd my-project

# Launch the AI assistant
chatproj launch

# Browser opens automatically at http://localhost:3000
```

## Commands

```bash
chatproj launch    # Start the AI assistant
chatproj init      # Initialize project configuration
chatproj stop      # Stop running instances
chatproj status    # Check running instances
```

## Setup

1. **Install the package**:
   ```bash
   npm install -g chatproj
   ```

2. **Set up OpenAI API key**:
   ```bash
   # Create .env file in your project or set environment variable
   echo "OPENAI_API_KEY=your_api_key_here" > .env
   ```

3. **Launch from any project**:
   ```bash
   cd your-project
   chatproj launch
   ```

## How It Works

1. CLI spawns a local Node.js server
2. Server serves a React SPA and opens your browser
3. WebSocket connection established for real-time communication
4. Chat with AI to create, edit, and manage your project files
5. AI responses are parsed for actionable commands
6. File operations executed safely within your project directory

## AI Capabilities

- **File Operations**: Create, update, delete files
- **Code Generation**: Generate components, functions, classes
- **Package Management**: Install dependencies
- **Code Analysis**: Review and refactor existing code
- **Project Structure**: Organize and restructure projects

## Security

- All operations restricted to project directory
- File paths sanitized and validated
- User confirmation for destructive actions
- Safe command execution with error handling

## Requirements

- Node.js 14+
- OpenAI API key

## License

MIT
