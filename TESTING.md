# ChatProj Testing Guide

This guide explains how to test Chat<PERSON><PERSON>j locally without publishing to npm.

## Prerequisites

Before testing, ensure you have:

- **Node.js 14+** installed
- **npm** or **yarn** package manager
- **OpenAI API key** (required for AI functionality)
- **Git** (optional, for version control testing)

## Quick Start Testing

### 1. Set Up Environment

```bash
# Clone or navigate to the project directory
cd chatproj

# Install dependencies
npm install

# Install client dependencies
cd client && npm install && cd ..

# Build the client
cd client && npm run build && cd ..
```

### 2. Configure Environment Variables

Create a `.env` file in the project root:

```bash
# Copy the example file
cp .env.example .env

# Edit .env and add your OpenAI API key
OPENAI_API_KEY=your_openai_api_key_here
PORT=3000
NODE_ENV=development
```

### 3. Test Locally (Without npm install)

You can test ChatProj directly from the development directory:

```bash
# Method 1: Use npm link (recommended)
npm link

# Now you can use 'chatproj' command globally
chatproj --help

# Method 2: Run directly with node
node cli.js --help

# Method 3: Use npm scripts
npm start  # Starts the server directly
```

## Testing Methods

### Method 1: Using npm link (Recommended)

This creates a global symlink to your local development version:

```bash
# In the project root
npm link

# Now test the CLI
chatproj init
chatproj launch
chatproj status
chatproj stop
```

**Advantages:**
- Tests the actual CLI experience
- Works from any directory
- Easy to update during development

### Method 2: Direct Node Execution

Run the CLI directly with Node.js:

```bash
# Test CLI commands
node cli.js init
node cli.js launch
node cli.js status
node cli.js stop

# Test with different options
node cli.js launch --port 3001
node cli.js launch --no-browser
```

### Method 3: Create Test Project

Create a separate test project to simulate real usage:

```bash
# Create test project
mkdir test-project
cd test-project
npm init -y

# Create some test files
echo "console.log('Hello World');" > index.js
echo "# Test Project" > README.md

# Test ChatProj in this directory
chatproj init
chatproj launch
```

## Testing Scenarios

### 1. Basic Functionality Test

```bash
# Initialize ChatProj
chatproj init

# Launch the assistant
chatproj launch

# Browser should open automatically to http://localhost:3000
# You should see the ChatProj interface
```

### 2. AI Integration Test

Once the interface is open:

1. **Test Connection**: Check that the connection status shows "Connected"
2. **Test Chat**: Send a simple message like "Hello, can you help me?"
3. **Test File Operations**: Ask "Create a simple JavaScript function"
4. **Test Commands**: Ask "What's in my package.json?"

### 3. File System Test

Test file operations:

```bash
# In the chat interface, try these prompts:
"Create a new file called test.js with a simple function"
"Show me the contents of package.json"
"Create a React component called Button"
"Install express as a dependency"
```

### 4. Security Test

Test security restrictions:

```bash
# These should be blocked:
"Delete all files in the project"
"Access files outside the project directory"
"Run rm -rf /"
"Show me /etc/passwd"
```

### 5. Error Handling Test

Test error scenarios:

```bash
# Test without OpenAI API key
# Remove OPENAI_API_KEY from .env and restart

# Test with invalid API key
# Set OPENAI_API_KEY=invalid_key and restart

# Test port conflicts
# Start two instances on the same port
```

## Running Unit Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- fileSystemManager.test.js
```

## Development Testing

### Hot Reload Development

For active development with hot reload:

```bash
# Terminal 1: Start server in development mode
npm run server:dev

# Terminal 2: Start client in development mode
cd client && npm run dev

# Terminal 3: Test CLI commands
node cli.js status
```

### Testing Client Changes

When modifying the React frontend:

```bash
# Build client after changes
cd client && npm run build && cd ..

# Restart server to serve new build
chatproj stop
chatproj launch
```

### Testing Server Changes

When modifying server code:

```bash
# Server will auto-restart with nodemon if using npm run server:dev
# Or manually restart:
chatproj stop
chatproj launch
```

## Testing Different Project Types

### Node.js Project

```bash
mkdir node-test && cd node-test
npm init -y
npm install express
chatproj launch
# Test: "Create an Express server"
```

### React Project

```bash
npx create-react-app react-test
cd react-test
chatproj launch
# Test: "Create a new React component"
```

### Empty Project

```bash
mkdir empty-test && cd empty-test
chatproj launch
# Test: "Initialize a new Node.js project"
```

## Debugging

### Enable Debug Logging

```bash
# Set debug environment variables
export DEBUG=chatproj:*
export LOG_LEVEL=debug

# Or in .env file
DEBUG=chatproj:*
LOG_LEVEL=debug
```

### Check Logs

```bash
# Server logs
tail -f chatproj.log

# Browser console
# Open browser dev tools (F12) and check console
```

### Common Issues

1. **Port Already in Use**
   ```bash
   # Find process using port 3000
   netstat -ano | findstr :3000  # Windows
   lsof -i :3000                 # macOS/Linux
   
   # Kill process or use different port
   chatproj launch --port 3001
   ```

2. **OpenAI API Errors**
   ```bash
   # Check API key
   echo $OPENAI_API_KEY
   
   # Test API key
   curl -H "Authorization: Bearer $OPENAI_API_KEY" \
        https://api.openai.com/v1/models
   ```

3. **File Permission Errors**
   ```bash
   # Check file permissions
   ls -la
   
   # Fix permissions if needed
   chmod 755 cli.js
   ```

## Performance Testing

### Load Testing

```bash
# Test multiple concurrent connections
# Use tools like Artillery or Apache Bench

# Install artillery
npm install -g artillery

# Create test config
echo "config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: 'Chat test'
    requests:
      - get:
          url: '/'" > load-test.yml

# Run load test
artillery run load-test.yml
```

### Memory Testing

```bash
# Monitor memory usage
node --inspect cli.js launch

# Use Chrome DevTools for memory profiling
# Open chrome://inspect in Chrome
```

## Automated Testing

### CI/CD Testing

```bash
# Test in CI environment
npm ci
npm run build
npm test
npm run lint
```

### Integration Testing

```bash
# Test full workflow
npm run test:integration

# Or manually:
chatproj init
chatproj launch --no-browser &
sleep 5
curl http://localhost:3000/api/health
chatproj stop
```

## Cleanup

After testing:

```bash
# Remove global link
npm unlink -g chatproj

# Clean up test files
rm -rf test-project node-test react-test empty-test

# Stop any running instances
chatproj stop
```

## Publishing Test (Optional)

To test the actual npm package experience:

```bash
# Pack the package locally
npm pack

# This creates chatproj-1.0.0.tgz

# Install from the tarball
npm install -g ./chatproj-1.0.0.tgz

# Test the installed package
chatproj --help
```

## Troubleshooting

### Reset Everything

```bash
# Stop all instances
chatproj stop

# Remove node_modules and reinstall
rm -rf node_modules client/node_modules
npm install
cd client && npm install && npm run build && cd ..

# Clear npm cache
npm cache clean --force

# Restart
chatproj launch
```

### Check System Requirements

```bash
# Check Node.js version
node --version  # Should be 14+

# Check npm version
npm --version

# Check available ports
netstat -an | grep LISTEN
```

This testing approach allows you to thoroughly test ChatProj without needing to publish to npm, making development and iteration much faster!
