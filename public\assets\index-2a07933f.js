function zv(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function Md(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ad={exports:{}},Hi={},jd={exports:{}},V={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var po=Symbol.for("react.element"),$v=Symbol.for("react.portal"),Uv=Symbol.for("react.fragment"),Wv=Symbol.for("react.strict_mode"),Vv=Symbol.for("react.profiler"),Bv=Symbol.for("react.provider"),Hv=Symbol.for("react.context"),Kv=Symbol.for("react.forward_ref"),Gv=Symbol.for("react.suspense"),Yv=Symbol.for("react.memo"),Xv=Symbol.for("react.lazy"),Uu=Symbol.iterator;function Qv(e){return e===null||typeof e!="object"?null:(e=Uu&&e[Uu]||e["@@iterator"],typeof e=="function"?e:null)}var Id={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Od=Object.assign,Ld={};function sr(e,t,n){this.props=e,this.context=t,this.refs=Ld,this.updater=n||Id}sr.prototype.isReactComponent={};sr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};sr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Dd(){}Dd.prototype=sr.prototype;function fa(e,t,n){this.props=e,this.context=t,this.refs=Ld,this.updater=n||Id}var pa=fa.prototype=new Dd;pa.constructor=fa;Od(pa,sr.prototype);pa.isPureReactComponent=!0;var Wu=Array.isArray,Fd=Object.prototype.hasOwnProperty,ma={current:null},zd={key:!0,ref:!0,__self:!0,__source:!0};function $d(e,t,n){var r,o={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)Fd.call(t,r)&&!zd.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:po,type:e,key:i,ref:l,props:o,_owner:ma.current}}function Zv(e,t){return{$$typeof:po,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ha(e){return typeof e=="object"&&e!==null&&e.$$typeof===po}function Jv(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Vu=/\/+/g;function yl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Jv(""+e.key):t.toString(36)}function Xo(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case po:case $v:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+yl(l,0):r,Wu(o)?(n="",e!=null&&(n=e.replace(Vu,"$&/")+"/"),Xo(o,t,n,"",function(u){return u})):o!=null&&(ha(o)&&(o=Zv(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(Vu,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",Wu(e))for(var s=0;s<e.length;s++){i=e[s];var a=r+yl(i,s);l+=Xo(i,t,n,a,o)}else if(a=Qv(e),typeof a=="function")for(e=a.call(e),s=0;!(i=e.next()).done;)i=i.value,a=r+yl(i,s++),l+=Xo(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function No(e,t,n){if(e==null)return e;var r=[],o=0;return Xo(e,r,"","",function(i){return t.call(n,i,o++)}),r}function qv(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Pe={current:null},Qo={transition:null},eg={ReactCurrentDispatcher:Pe,ReactCurrentBatchConfig:Qo,ReactCurrentOwner:ma};function Ud(){throw Error("act(...) is not supported in production builds of React.")}V.Children={map:No,forEach:function(e,t,n){No(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return No(e,function(){t++}),t},toArray:function(e){return No(e,function(t){return t})||[]},only:function(e){if(!ha(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};V.Component=sr;V.Fragment=Uv;V.Profiler=Vv;V.PureComponent=fa;V.StrictMode=Wv;V.Suspense=Gv;V.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eg;V.act=Ud;V.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Od({},e.props),o=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=ma.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)Fd.call(t,a)&&!zd.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:po,type:e.type,key:o,ref:i,props:r,_owner:l}};V.createContext=function(e){return e={$$typeof:Hv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Bv,_context:e},e.Consumer=e};V.createElement=$d;V.createFactory=function(e){var t=$d.bind(null,e);return t.type=e,t};V.createRef=function(){return{current:null}};V.forwardRef=function(e){return{$$typeof:Kv,render:e}};V.isValidElement=ha;V.lazy=function(e){return{$$typeof:Xv,_payload:{_status:-1,_result:e},_init:qv}};V.memo=function(e,t){return{$$typeof:Yv,type:e,compare:t===void 0?null:t}};V.startTransition=function(e){var t=Qo.transition;Qo.transition={};try{e()}finally{Qo.transition=t}};V.unstable_act=Ud;V.useCallback=function(e,t){return Pe.current.useCallback(e,t)};V.useContext=function(e){return Pe.current.useContext(e)};V.useDebugValue=function(){};V.useDeferredValue=function(e){return Pe.current.useDeferredValue(e)};V.useEffect=function(e,t){return Pe.current.useEffect(e,t)};V.useId=function(){return Pe.current.useId()};V.useImperativeHandle=function(e,t,n){return Pe.current.useImperativeHandle(e,t,n)};V.useInsertionEffect=function(e,t){return Pe.current.useInsertionEffect(e,t)};V.useLayoutEffect=function(e,t){return Pe.current.useLayoutEffect(e,t)};V.useMemo=function(e,t){return Pe.current.useMemo(e,t)};V.useReducer=function(e,t,n){return Pe.current.useReducer(e,t,n)};V.useRef=function(e){return Pe.current.useRef(e)};V.useState=function(e){return Pe.current.useState(e)};V.useSyncExternalStore=function(e,t,n){return Pe.current.useSyncExternalStore(e,t,n)};V.useTransition=function(){return Pe.current.useTransition()};V.version="18.3.1";jd.exports=V;var p=jd.exports;const Et=Md(p),Wd=zv({__proto__:null,default:Et},[p]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tg=p,ng=Symbol.for("react.element"),rg=Symbol.for("react.fragment"),og=Object.prototype.hasOwnProperty,ig=tg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,lg={key:!0,ref:!0,__self:!0,__source:!0};function Vd(e,t,n){var r,o={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)og.call(t,r)&&!lg.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:ng,type:e,key:i,ref:l,props:o,_owner:ig.current}}Hi.Fragment=rg;Hi.jsx=Vd;Hi.jsxs=Vd;Ad.exports=Hi;var d=Ad.exports,is={},Bd={exports:{}},Ue={},Hd={exports:{}},Kd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(C,R){var A=C.length;C.push(R);e:for(;0<A;){var z=A-1>>>1,H=C[z];if(0<o(H,R))C[z]=R,C[A]=H,A=z;else break e}}function n(C){return C.length===0?null:C[0]}function r(C){if(C.length===0)return null;var R=C[0],A=C.pop();if(A!==R){C[0]=A;e:for(var z=0,H=C.length,Ee=H>>>1;z<Ee;){var ge=2*(z+1)-1,Ot=C[ge],Ce=ge+1,be=C[Ce];if(0>o(Ot,A))Ce<H&&0>o(be,Ot)?(C[z]=be,C[Ce]=A,z=Ce):(C[z]=Ot,C[ge]=A,z=ge);else if(Ce<H&&0>o(be,A))C[z]=be,C[Ce]=A,z=Ce;else break e}}return R}function o(C,R){var A=C.sortIndex-R.sortIndex;return A!==0?A:C.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],f=1,c=null,m=3,w=!1,x=!1,y=!1,S=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(C){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=C)r(u),R.sortIndex=R.expirationTime,t(a,R);else break;R=n(u)}}function E(C){if(y=!1,g(C),!x)if(n(a)!==null)x=!0,F(k);else{var R=n(u);R!==null&&B(E,R.startTime-C)}}function k(C,R){x=!1,y&&(y=!1,v(T),T=-1),w=!0;var A=m;try{for(g(R),c=n(a);c!==null&&(!(c.expirationTime>R)||C&&!L());){var z=c.callback;if(typeof z=="function"){c.callback=null,m=c.priorityLevel;var H=z(c.expirationTime<=R);R=e.unstable_now(),typeof H=="function"?c.callback=H:c===n(a)&&r(a),g(R)}else r(a);c=n(a)}if(c!==null)var Ee=!0;else{var ge=n(u);ge!==null&&B(E,ge.startTime-R),Ee=!1}return Ee}finally{c=null,m=A,w=!1}}var P=!1,N=null,T=-1,M=5,_=-1;function L(){return!(e.unstable_now()-_<M)}function O(){if(N!==null){var C=e.unstable_now();_=C;var R=!0;try{R=N(!0,C)}finally{R?U():(P=!1,N=null)}}else P=!1}var U;if(typeof h=="function")U=function(){h(O)};else if(typeof MessageChannel<"u"){var j=new MessageChannel,W=j.port2;j.port1.onmessage=O,U=function(){W.postMessage(null)}}else U=function(){S(O,0)};function F(C){N=C,P||(P=!0,U())}function B(C,R){T=S(function(){C(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(C){C.callback=null},e.unstable_continueExecution=function(){x||w||(x=!0,F(k))},e.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<C?Math.floor(1e3/C):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(C){switch(m){case 1:case 2:case 3:var R=3;break;default:R=m}var A=m;m=R;try{return C()}finally{m=A}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(C,R){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var A=m;m=C;try{return R()}finally{m=A}},e.unstable_scheduleCallback=function(C,R,A){var z=e.unstable_now();switch(typeof A=="object"&&A!==null?(A=A.delay,A=typeof A=="number"&&0<A?z+A:z):A=z,C){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=A+H,C={id:f++,callback:R,priorityLevel:C,startTime:A,expirationTime:H,sortIndex:-1},A>z?(C.sortIndex=A,t(u,C),n(a)===null&&C===n(u)&&(y?(v(T),T=-1):y=!0,B(E,A-z))):(C.sortIndex=H,t(a,C),x||w||(x=!0,F(k))),C},e.unstable_shouldYield=L,e.unstable_wrapCallback=function(C){var R=m;return function(){var A=m;m=R;try{return C.apply(this,arguments)}finally{m=A}}}})(Kd);Hd.exports=Kd;var sg=Hd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ag=p,$e=sg;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Gd=new Set,Ur={};function En(e,t){Jn(e,t),Jn(e+"Capture",t)}function Jn(e,t){for(Ur[e]=t,e=0;e<t.length;e++)Gd.add(t[e])}var Tt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ls=Object.prototype.hasOwnProperty,ug=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Bu={},Hu={};function cg(e){return ls.call(Hu,e)?!0:ls.call(Bu,e)?!1:ug.test(e)?Hu[e]=!0:(Bu[e]=!0,!1)}function dg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function fg(e,t,n,r){if(t===null||typeof t>"u"||dg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Re(e,t,n,r,o,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var ve={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ve[e]=new Re(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ve[t]=new Re(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ve[e]=new Re(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ve[e]=new Re(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ve[e]=new Re(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ve[e]=new Re(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ve[e]=new Re(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ve[e]=new Re(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ve[e]=new Re(e,5,!1,e.toLowerCase(),null,!1,!1)});var va=/[\-:]([a-z])/g;function ga(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(va,ga);ve[t]=new Re(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(va,ga);ve[t]=new Re(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(va,ga);ve[t]=new Re(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ve[e]=new Re(e,1,!1,e.toLowerCase(),null,!1,!1)});ve.xlinkHref=new Re("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ve[e]=new Re(e,1,!1,e.toLowerCase(),null,!0,!0)});function ya(e,t,n,r){var o=ve.hasOwnProperty(t)?ve[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(fg(t,n,o,r)&&(n=null),r||o===null?cg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var It=ag.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Po=Symbol.for("react.element"),Mn=Symbol.for("react.portal"),An=Symbol.for("react.fragment"),wa=Symbol.for("react.strict_mode"),ss=Symbol.for("react.profiler"),Yd=Symbol.for("react.provider"),Xd=Symbol.for("react.context"),xa=Symbol.for("react.forward_ref"),as=Symbol.for("react.suspense"),us=Symbol.for("react.suspense_list"),Sa=Symbol.for("react.memo"),$t=Symbol.for("react.lazy"),Qd=Symbol.for("react.offscreen"),Ku=Symbol.iterator;function vr(e){return e===null||typeof e!="object"?null:(e=Ku&&e[Ku]||e["@@iterator"],typeof e=="function"?e:null)}var oe=Object.assign,wl;function Pr(e){if(wl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);wl=t&&t[1]||""}return`
`+wl+e}var xl=!1;function Sl(e,t){if(!e||xl)return"";xl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),l=o.length-1,s=i.length-1;1<=l&&0<=s&&o[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(o[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||o[l]!==i[s]){var a=`
`+o[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{xl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Pr(e):""}function pg(e){switch(e.tag){case 5:return Pr(e.type);case 16:return Pr("Lazy");case 13:return Pr("Suspense");case 19:return Pr("SuspenseList");case 0:case 2:case 15:return e=Sl(e.type,!1),e;case 11:return e=Sl(e.type.render,!1),e;case 1:return e=Sl(e.type,!0),e;default:return""}}function cs(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case An:return"Fragment";case Mn:return"Portal";case ss:return"Profiler";case wa:return"StrictMode";case as:return"Suspense";case us:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Xd:return(e.displayName||"Context")+".Consumer";case Yd:return(e._context.displayName||"Context")+".Provider";case xa:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Sa:return t=e.displayName||null,t!==null?t:cs(e.type)||"Memo";case $t:t=e._payload,e=e._init;try{return cs(e(t))}catch{}}return null}function mg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return cs(t);case 8:return t===wa?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function en(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Zd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function hg(e){var t=Zd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ro(e){e._valueTracker||(e._valueTracker=hg(e))}function Jd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Zd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function pi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ds(e,t){var n=t.checked;return oe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Gu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=en(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function qd(e,t){t=t.checked,t!=null&&ya(e,"checked",t,!1)}function fs(e,t){qd(e,t);var n=en(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ps(e,t.type,n):t.hasOwnProperty("defaultValue")&&ps(e,t.type,en(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Yu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ps(e,t,n){(t!=="number"||pi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Rr=Array.isArray;function Vn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+en(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ms(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(b(91));return oe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Xu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(b(92));if(Rr(n)){if(1<n.length)throw Error(b(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:en(n)}}function ef(e,t){var n=en(t.value),r=en(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Qu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function tf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function hs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?tf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var To,nf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(To=To||document.createElement("div"),To.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=To.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Wr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Mr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},vg=["Webkit","ms","Moz","O"];Object.keys(Mr).forEach(function(e){vg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Mr[t]=Mr[e]})});function rf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Mr.hasOwnProperty(e)&&Mr[e]?(""+t).trim():t+"px"}function of(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=rf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var gg=oe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function vs(e,t){if(t){if(gg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(b(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(b(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(b(61))}if(t.style!=null&&typeof t.style!="object")throw Error(b(62))}}function gs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ys=null;function Ea(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ws=null,Bn=null,Hn=null;function Zu(e){if(e=vo(e)){if(typeof ws!="function")throw Error(b(280));var t=e.stateNode;t&&(t=Qi(t),ws(e.stateNode,e.type,t))}}function lf(e){Bn?Hn?Hn.push(e):Hn=[e]:Bn=e}function sf(){if(Bn){var e=Bn,t=Hn;if(Hn=Bn=null,Zu(e),t)for(e=0;e<t.length;e++)Zu(t[e])}}function af(e,t){return e(t)}function uf(){}var El=!1;function cf(e,t,n){if(El)return e(t,n);El=!0;try{return af(e,t,n)}finally{El=!1,(Bn!==null||Hn!==null)&&(uf(),sf())}}function Vr(e,t){var n=e.stateNode;if(n===null)return null;var r=Qi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(b(231,t,typeof n));return n}var xs=!1;if(Tt)try{var gr={};Object.defineProperty(gr,"passive",{get:function(){xs=!0}}),window.addEventListener("test",gr,gr),window.removeEventListener("test",gr,gr)}catch{xs=!1}function yg(e,t,n,r,o,i,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var Ar=!1,mi=null,hi=!1,Ss=null,wg={onError:function(e){Ar=!0,mi=e}};function xg(e,t,n,r,o,i,l,s,a){Ar=!1,mi=null,yg.apply(wg,arguments)}function Sg(e,t,n,r,o,i,l,s,a){if(xg.apply(this,arguments),Ar){if(Ar){var u=mi;Ar=!1,mi=null}else throw Error(b(198));hi||(hi=!0,Ss=u)}}function Cn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function df(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ju(e){if(Cn(e)!==e)throw Error(b(188))}function Eg(e){var t=e.alternate;if(!t){if(t=Cn(e),t===null)throw Error(b(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Ju(o),e;if(i===r)return Ju(o),t;i=i.sibling}throw Error(b(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(b(189))}}if(n.alternate!==r)throw Error(b(190))}if(n.tag!==3)throw Error(b(188));return n.stateNode.current===n?e:t}function ff(e){return e=Eg(e),e!==null?pf(e):null}function pf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=pf(e);if(t!==null)return t;e=e.sibling}return null}var mf=$e.unstable_scheduleCallback,qu=$e.unstable_cancelCallback,Cg=$e.unstable_shouldYield,kg=$e.unstable_requestPaint,se=$e.unstable_now,Ng=$e.unstable_getCurrentPriorityLevel,Ca=$e.unstable_ImmediatePriority,hf=$e.unstable_UserBlockingPriority,vi=$e.unstable_NormalPriority,Pg=$e.unstable_LowPriority,vf=$e.unstable_IdlePriority,Ki=null,pt=null;function Rg(e){if(pt&&typeof pt.onCommitFiberRoot=="function")try{pt.onCommitFiberRoot(Ki,e,void 0,(e.current.flags&128)===128)}catch{}}var nt=Math.clz32?Math.clz32:_g,Tg=Math.log,bg=Math.LN2;function _g(e){return e>>>=0,e===0?32:31-(Tg(e)/bg|0)|0}var bo=64,_o=4194304;function Tr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function gi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~o;s!==0?r=Tr(s):(i&=l,i!==0&&(r=Tr(i)))}else l=n&~o,l!==0?r=Tr(l):i!==0&&(r=Tr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-nt(t),o=1<<n,r|=e[n],t&=~o;return r}function Mg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ag(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-nt(i),s=1<<l,a=o[l];a===-1?(!(s&n)||s&r)&&(o[l]=Mg(s,t)):a<=t&&(e.expiredLanes|=s),i&=~s}}function Es(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function gf(){var e=bo;return bo<<=1,!(bo&4194240)&&(bo=64),e}function Cl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function mo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-nt(t),e[t]=n}function jg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-nt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function ka(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-nt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var G=0;function yf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var wf,Na,xf,Sf,Ef,Cs=!1,Mo=[],Kt=null,Gt=null,Yt=null,Br=new Map,Hr=new Map,Wt=[],Ig="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ec(e,t){switch(e){case"focusin":case"focusout":Kt=null;break;case"dragenter":case"dragleave":Gt=null;break;case"mouseover":case"mouseout":Yt=null;break;case"pointerover":case"pointerout":Br.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hr.delete(t.pointerId)}}function yr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=vo(t),t!==null&&Na(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Og(e,t,n,r,o){switch(t){case"focusin":return Kt=yr(Kt,e,t,n,r,o),!0;case"dragenter":return Gt=yr(Gt,e,t,n,r,o),!0;case"mouseover":return Yt=yr(Yt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Br.set(i,yr(Br.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Hr.set(i,yr(Hr.get(i)||null,e,t,n,r,o)),!0}return!1}function Cf(e){var t=dn(e.target);if(t!==null){var n=Cn(t);if(n!==null){if(t=n.tag,t===13){if(t=df(n),t!==null){e.blockedOn=t,Ef(e.priority,function(){xf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Zo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ks(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ys=r,n.target.dispatchEvent(r),ys=null}else return t=vo(n),t!==null&&Na(t),e.blockedOn=n,!1;t.shift()}return!0}function tc(e,t,n){Zo(e)&&n.delete(t)}function Lg(){Cs=!1,Kt!==null&&Zo(Kt)&&(Kt=null),Gt!==null&&Zo(Gt)&&(Gt=null),Yt!==null&&Zo(Yt)&&(Yt=null),Br.forEach(tc),Hr.forEach(tc)}function wr(e,t){e.blockedOn===t&&(e.blockedOn=null,Cs||(Cs=!0,$e.unstable_scheduleCallback($e.unstable_NormalPriority,Lg)))}function Kr(e){function t(o){return wr(o,e)}if(0<Mo.length){wr(Mo[0],e);for(var n=1;n<Mo.length;n++){var r=Mo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Kt!==null&&wr(Kt,e),Gt!==null&&wr(Gt,e),Yt!==null&&wr(Yt,e),Br.forEach(t),Hr.forEach(t),n=0;n<Wt.length;n++)r=Wt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Wt.length&&(n=Wt[0],n.blockedOn===null);)Cf(n),n.blockedOn===null&&Wt.shift()}var Kn=It.ReactCurrentBatchConfig,yi=!0;function Dg(e,t,n,r){var o=G,i=Kn.transition;Kn.transition=null;try{G=1,Pa(e,t,n,r)}finally{G=o,Kn.transition=i}}function Fg(e,t,n,r){var o=G,i=Kn.transition;Kn.transition=null;try{G=4,Pa(e,t,n,r)}finally{G=o,Kn.transition=i}}function Pa(e,t,n,r){if(yi){var o=ks(e,t,n,r);if(o===null)jl(e,t,r,wi,n),ec(e,r);else if(Og(o,e,t,n,r))r.stopPropagation();else if(ec(e,r),t&4&&-1<Ig.indexOf(e)){for(;o!==null;){var i=vo(o);if(i!==null&&wf(i),i=ks(e,t,n,r),i===null&&jl(e,t,r,wi,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else jl(e,t,r,null,n)}}var wi=null;function ks(e,t,n,r){if(wi=null,e=Ea(r),e=dn(e),e!==null)if(t=Cn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=df(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return wi=e,null}function kf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ng()){case Ca:return 1;case hf:return 4;case vi:case Pg:return 16;case vf:return 536870912;default:return 16}default:return 16}}var Bt=null,Ra=null,Jo=null;function Nf(){if(Jo)return Jo;var e,t=Ra,n=t.length,r,o="value"in Bt?Bt.value:Bt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[i-r];r++);return Jo=o.slice(e,1<r?1-r:void 0)}function qo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ao(){return!0}function nc(){return!1}function We(e){function t(n,r,o,i,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ao:nc,this.isPropagationStopped=nc,this}return oe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ao)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ao)},persist:function(){},isPersistent:Ao}),t}var ar={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ta=We(ar),ho=oe({},ar,{view:0,detail:0}),zg=We(ho),kl,Nl,xr,Gi=oe({},ho,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ba,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==xr&&(xr&&e.type==="mousemove"?(kl=e.screenX-xr.screenX,Nl=e.screenY-xr.screenY):Nl=kl=0,xr=e),kl)},movementY:function(e){return"movementY"in e?e.movementY:Nl}}),rc=We(Gi),$g=oe({},Gi,{dataTransfer:0}),Ug=We($g),Wg=oe({},ho,{relatedTarget:0}),Pl=We(Wg),Vg=oe({},ar,{animationName:0,elapsedTime:0,pseudoElement:0}),Bg=We(Vg),Hg=oe({},ar,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Kg=We(Hg),Gg=oe({},ar,{data:0}),oc=We(Gg),Yg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Xg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Qg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Zg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Qg[e])?!!t[e]:!1}function ba(){return Zg}var Jg=oe({},ho,{key:function(e){if(e.key){var t=Yg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=qo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Xg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ba,charCode:function(e){return e.type==="keypress"?qo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?qo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),qg=We(Jg),ey=oe({},Gi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ic=We(ey),ty=oe({},ho,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ba}),ny=We(ty),ry=oe({},ar,{propertyName:0,elapsedTime:0,pseudoElement:0}),oy=We(ry),iy=oe({},Gi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ly=We(iy),sy=[9,13,27,32],_a=Tt&&"CompositionEvent"in window,jr=null;Tt&&"documentMode"in document&&(jr=document.documentMode);var ay=Tt&&"TextEvent"in window&&!jr,Pf=Tt&&(!_a||jr&&8<jr&&11>=jr),lc=String.fromCharCode(32),sc=!1;function Rf(e,t){switch(e){case"keyup":return sy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var jn=!1;function uy(e,t){switch(e){case"compositionend":return Tf(t);case"keypress":return t.which!==32?null:(sc=!0,lc);case"textInput":return e=t.data,e===lc&&sc?null:e;default:return null}}function cy(e,t){if(jn)return e==="compositionend"||!_a&&Rf(e,t)?(e=Nf(),Jo=Ra=Bt=null,jn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Pf&&t.locale!=="ko"?null:t.data;default:return null}}var dy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ac(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!dy[e.type]:t==="textarea"}function bf(e,t,n,r){lf(r),t=xi(t,"onChange"),0<t.length&&(n=new Ta("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ir=null,Gr=null;function fy(e){$f(e,0)}function Yi(e){var t=Ln(e);if(Jd(t))return e}function py(e,t){if(e==="change")return t}var _f=!1;if(Tt){var Rl;if(Tt){var Tl="oninput"in document;if(!Tl){var uc=document.createElement("div");uc.setAttribute("oninput","return;"),Tl=typeof uc.oninput=="function"}Rl=Tl}else Rl=!1;_f=Rl&&(!document.documentMode||9<document.documentMode)}function cc(){Ir&&(Ir.detachEvent("onpropertychange",Mf),Gr=Ir=null)}function Mf(e){if(e.propertyName==="value"&&Yi(Gr)){var t=[];bf(t,Gr,e,Ea(e)),cf(fy,t)}}function my(e,t,n){e==="focusin"?(cc(),Ir=t,Gr=n,Ir.attachEvent("onpropertychange",Mf)):e==="focusout"&&cc()}function hy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Yi(Gr)}function vy(e,t){if(e==="click")return Yi(t)}function gy(e,t){if(e==="input"||e==="change")return Yi(t)}function yy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ot=typeof Object.is=="function"?Object.is:yy;function Yr(e,t){if(ot(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ls.call(t,o)||!ot(e[o],t[o]))return!1}return!0}function dc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function fc(e,t){var n=dc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=dc(n)}}function Af(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Af(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function jf(){for(var e=window,t=pi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=pi(e.document)}return t}function Ma(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function wy(e){var t=jf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Af(n.ownerDocument.documentElement,n)){if(r!==null&&Ma(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=fc(n,i);var l=fc(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var xy=Tt&&"documentMode"in document&&11>=document.documentMode,In=null,Ns=null,Or=null,Ps=!1;function pc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ps||In==null||In!==pi(r)||(r=In,"selectionStart"in r&&Ma(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Or&&Yr(Or,r)||(Or=r,r=xi(Ns,"onSelect"),0<r.length&&(t=new Ta("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=In)))}function jo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var On={animationend:jo("Animation","AnimationEnd"),animationiteration:jo("Animation","AnimationIteration"),animationstart:jo("Animation","AnimationStart"),transitionend:jo("Transition","TransitionEnd")},bl={},If={};Tt&&(If=document.createElement("div").style,"AnimationEvent"in window||(delete On.animationend.animation,delete On.animationiteration.animation,delete On.animationstart.animation),"TransitionEvent"in window||delete On.transitionend.transition);function Xi(e){if(bl[e])return bl[e];if(!On[e])return e;var t=On[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in If)return bl[e]=t[n];return e}var Of=Xi("animationend"),Lf=Xi("animationiteration"),Df=Xi("animationstart"),Ff=Xi("transitionend"),zf=new Map,mc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function on(e,t){zf.set(e,t),En(t,[e])}for(var _l=0;_l<mc.length;_l++){var Ml=mc[_l],Sy=Ml.toLowerCase(),Ey=Ml[0].toUpperCase()+Ml.slice(1);on(Sy,"on"+Ey)}on(Of,"onAnimationEnd");on(Lf,"onAnimationIteration");on(Df,"onAnimationStart");on("dblclick","onDoubleClick");on("focusin","onFocus");on("focusout","onBlur");on(Ff,"onTransitionEnd");Jn("onMouseEnter",["mouseout","mouseover"]);Jn("onMouseLeave",["mouseout","mouseover"]);Jn("onPointerEnter",["pointerout","pointerover"]);Jn("onPointerLeave",["pointerout","pointerover"]);En("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));En("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));En("onBeforeInput",["compositionend","keypress","textInput","paste"]);En("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));En("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));En("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var br="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Cy=new Set("cancel close invalid load scroll toggle".split(" ").concat(br));function hc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Sg(r,t,void 0,e),e.currentTarget=null}function $f(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==i&&o.isPropagationStopped())break e;hc(o,s,u),i=a}else for(l=0;l<r.length;l++){if(s=r[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==i&&o.isPropagationStopped())break e;hc(o,s,u),i=a}}}if(hi)throw e=Ss,hi=!1,Ss=null,e}function q(e,t){var n=t[Ms];n===void 0&&(n=t[Ms]=new Set);var r=e+"__bubble";n.has(r)||(Uf(t,e,2,!1),n.add(r))}function Al(e,t,n){var r=0;t&&(r|=4),Uf(n,e,r,t)}var Io="_reactListening"+Math.random().toString(36).slice(2);function Xr(e){if(!e[Io]){e[Io]=!0,Gd.forEach(function(n){n!=="selectionchange"&&(Cy.has(n)||Al(n,!1,e),Al(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Io]||(t[Io]=!0,Al("selectionchange",!1,t))}}function Uf(e,t,n,r){switch(kf(t)){case 1:var o=Dg;break;case 4:o=Fg;break;default:o=Pa}n=o.bind(null,t,n,e),o=void 0,!xs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function jl(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;l=l.return}for(;s!==null;){if(l=dn(s),l===null)return;if(a=l.tag,a===5||a===6){r=i=l;continue e}s=s.parentNode}}r=r.return}cf(function(){var u=i,f=Ea(n),c=[];e:{var m=zf.get(e);if(m!==void 0){var w=Ta,x=e;switch(e){case"keypress":if(qo(n)===0)break e;case"keydown":case"keyup":w=qg;break;case"focusin":x="focus",w=Pl;break;case"focusout":x="blur",w=Pl;break;case"beforeblur":case"afterblur":w=Pl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=rc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=Ug;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=ny;break;case Of:case Lf:case Df:w=Bg;break;case Ff:w=oy;break;case"scroll":w=zg;break;case"wheel":w=ly;break;case"copy":case"cut":case"paste":w=Kg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=ic}var y=(t&4)!==0,S=!y&&e==="scroll",v=y?m!==null?m+"Capture":null:m;y=[];for(var h=u,g;h!==null;){g=h;var E=g.stateNode;if(g.tag===5&&E!==null&&(g=E,v!==null&&(E=Vr(h,v),E!=null&&y.push(Qr(h,E,g)))),S)break;h=h.return}0<y.length&&(m=new w(m,x,null,n,f),c.push({event:m,listeners:y}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",m&&n!==ys&&(x=n.relatedTarget||n.fromElement)&&(dn(x)||x[bt]))break e;if((w||m)&&(m=f.window===f?f:(m=f.ownerDocument)?m.defaultView||m.parentWindow:window,w?(x=n.relatedTarget||n.toElement,w=u,x=x?dn(x):null,x!==null&&(S=Cn(x),x!==S||x.tag!==5&&x.tag!==6)&&(x=null)):(w=null,x=u),w!==x)){if(y=rc,E="onMouseLeave",v="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(y=ic,E="onPointerLeave",v="onPointerEnter",h="pointer"),S=w==null?m:Ln(w),g=x==null?m:Ln(x),m=new y(E,h+"leave",w,n,f),m.target=S,m.relatedTarget=g,E=null,dn(f)===u&&(y=new y(v,h+"enter",x,n,f),y.target=g,y.relatedTarget=S,E=y),S=E,w&&x)t:{for(y=w,v=x,h=0,g=y;g;g=Rn(g))h++;for(g=0,E=v;E;E=Rn(E))g++;for(;0<h-g;)y=Rn(y),h--;for(;0<g-h;)v=Rn(v),g--;for(;h--;){if(y===v||v!==null&&y===v.alternate)break t;y=Rn(y),v=Rn(v)}y=null}else y=null;w!==null&&vc(c,m,w,y,!1),x!==null&&S!==null&&vc(c,S,x,y,!0)}}e:{if(m=u?Ln(u):window,w=m.nodeName&&m.nodeName.toLowerCase(),w==="select"||w==="input"&&m.type==="file")var k=py;else if(ac(m))if(_f)k=gy;else{k=hy;var P=my}else(w=m.nodeName)&&w.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(k=vy);if(k&&(k=k(e,u))){bf(c,k,n,f);break e}P&&P(e,m,u),e==="focusout"&&(P=m._wrapperState)&&P.controlled&&m.type==="number"&&ps(m,"number",m.value)}switch(P=u?Ln(u):window,e){case"focusin":(ac(P)||P.contentEditable==="true")&&(In=P,Ns=u,Or=null);break;case"focusout":Or=Ns=In=null;break;case"mousedown":Ps=!0;break;case"contextmenu":case"mouseup":case"dragend":Ps=!1,pc(c,n,f);break;case"selectionchange":if(xy)break;case"keydown":case"keyup":pc(c,n,f)}var N;if(_a)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else jn?Rf(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Pf&&n.locale!=="ko"&&(jn||T!=="onCompositionStart"?T==="onCompositionEnd"&&jn&&(N=Nf()):(Bt=f,Ra="value"in Bt?Bt.value:Bt.textContent,jn=!0)),P=xi(u,T),0<P.length&&(T=new oc(T,e,null,n,f),c.push({event:T,listeners:P}),N?T.data=N:(N=Tf(n),N!==null&&(T.data=N)))),(N=ay?uy(e,n):cy(e,n))&&(u=xi(u,"onBeforeInput"),0<u.length&&(f=new oc("onBeforeInput","beforeinput",null,n,f),c.push({event:f,listeners:u}),f.data=N))}$f(c,t)})}function Qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function xi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Vr(e,n),i!=null&&r.unshift(Qr(e,i,o)),i=Vr(e,t),i!=null&&r.push(Qr(e,i,o))),e=e.return}return r}function Rn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function vc(e,t,n,r,o){for(var i=t._reactName,l=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,o?(a=Vr(n,i),a!=null&&l.unshift(Qr(n,a,s))):o||(a=Vr(n,i),a!=null&&l.push(Qr(n,a,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var ky=/\r\n?/g,Ny=/\u0000|\uFFFD/g;function gc(e){return(typeof e=="string"?e:""+e).replace(ky,`
`).replace(Ny,"")}function Oo(e,t,n){if(t=gc(t),gc(e)!==t&&n)throw Error(b(425))}function Si(){}var Rs=null,Ts=null;function bs(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var _s=typeof setTimeout=="function"?setTimeout:void 0,Py=typeof clearTimeout=="function"?clearTimeout:void 0,yc=typeof Promise=="function"?Promise:void 0,Ry=typeof queueMicrotask=="function"?queueMicrotask:typeof yc<"u"?function(e){return yc.resolve(null).then(e).catch(Ty)}:_s;function Ty(e){setTimeout(function(){throw e})}function Il(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Kr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Kr(t)}function Xt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function wc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ur=Math.random().toString(36).slice(2),ut="__reactFiber$"+ur,Zr="__reactProps$"+ur,bt="__reactContainer$"+ur,Ms="__reactEvents$"+ur,by="__reactListeners$"+ur,_y="__reactHandles$"+ur;function dn(e){var t=e[ut];if(t)return t;for(var n=e.parentNode;n;){if(t=n[bt]||n[ut]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=wc(e);e!==null;){if(n=e[ut])return n;e=wc(e)}return t}e=n,n=e.parentNode}return null}function vo(e){return e=e[ut]||e[bt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Ln(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(b(33))}function Qi(e){return e[Zr]||null}var As=[],Dn=-1;function ln(e){return{current:e}}function ee(e){0>Dn||(e.current=As[Dn],As[Dn]=null,Dn--)}function Q(e,t){Dn++,As[Dn]=e.current,e.current=t}var tn={},Se=ln(tn),Ae=ln(!1),vn=tn;function qn(e,t){var n=e.type.contextTypes;if(!n)return tn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function je(e){return e=e.childContextTypes,e!=null}function Ei(){ee(Ae),ee(Se)}function xc(e,t,n){if(Se.current!==tn)throw Error(b(168));Q(Se,t),Q(Ae,n)}function Wf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(b(108,mg(e)||"Unknown",o));return oe({},n,r)}function Ci(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||tn,vn=Se.current,Q(Se,e),Q(Ae,Ae.current),!0}function Sc(e,t,n){var r=e.stateNode;if(!r)throw Error(b(169));n?(e=Wf(e,t,vn),r.__reactInternalMemoizedMergedChildContext=e,ee(Ae),ee(Se),Q(Se,e)):ee(Ae),Q(Ae,n)}var kt=null,Zi=!1,Ol=!1;function Vf(e){kt===null?kt=[e]:kt.push(e)}function My(e){Zi=!0,Vf(e)}function sn(){if(!Ol&&kt!==null){Ol=!0;var e=0,t=G;try{var n=kt;for(G=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}kt=null,Zi=!1}catch(o){throw kt!==null&&(kt=kt.slice(e+1)),mf(Ca,sn),o}finally{G=t,Ol=!1}}return null}var Fn=[],zn=0,ki=null,Ni=0,Be=[],He=0,gn=null,Nt=1,Pt="";function an(e,t){Fn[zn++]=Ni,Fn[zn++]=ki,ki=e,Ni=t}function Bf(e,t,n){Be[He++]=Nt,Be[He++]=Pt,Be[He++]=gn,gn=e;var r=Nt;e=Pt;var o=32-nt(r)-1;r&=~(1<<o),n+=1;var i=32-nt(t)+o;if(30<i){var l=o-o%5;i=(r&(1<<l)-1).toString(32),r>>=l,o-=l,Nt=1<<32-nt(t)+o|n<<o|r,Pt=i+e}else Nt=1<<i|n<<o|r,Pt=e}function Aa(e){e.return!==null&&(an(e,1),Bf(e,1,0))}function ja(e){for(;e===ki;)ki=Fn[--zn],Fn[zn]=null,Ni=Fn[--zn],Fn[zn]=null;for(;e===gn;)gn=Be[--He],Be[He]=null,Pt=Be[--He],Be[He]=null,Nt=Be[--He],Be[He]=null}var Fe=null,De=null,te=!1,tt=null;function Hf(e,t){var n=Ke(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ec(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Fe=e,De=Xt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Fe=e,De=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=gn!==null?{id:Nt,overflow:Pt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ke(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Fe=e,De=null,!0):!1;default:return!1}}function js(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Is(e){if(te){var t=De;if(t){var n=t;if(!Ec(e,t)){if(js(e))throw Error(b(418));t=Xt(n.nextSibling);var r=Fe;t&&Ec(e,t)?Hf(r,n):(e.flags=e.flags&-4097|2,te=!1,Fe=e)}}else{if(js(e))throw Error(b(418));e.flags=e.flags&-4097|2,te=!1,Fe=e}}}function Cc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Fe=e}function Lo(e){if(e!==Fe)return!1;if(!te)return Cc(e),te=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!bs(e.type,e.memoizedProps)),t&&(t=De)){if(js(e))throw Kf(),Error(b(418));for(;t;)Hf(e,t),t=Xt(t.nextSibling)}if(Cc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(b(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){De=Xt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}De=null}}else De=Fe?Xt(e.stateNode.nextSibling):null;return!0}function Kf(){for(var e=De;e;)e=Xt(e.nextSibling)}function er(){De=Fe=null,te=!1}function Ia(e){tt===null?tt=[e]:tt.push(e)}var Ay=It.ReactCurrentBatchConfig;function Sr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(b(309));var r=n.stateNode}if(!r)throw Error(b(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=o.refs;l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(b(284));if(!n._owner)throw Error(b(290,e))}return e}function Do(e,t){throw e=Object.prototype.toString.call(t),Error(b(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function kc(e){var t=e._init;return t(e._payload)}function Gf(e){function t(v,h){if(e){var g=v.deletions;g===null?(v.deletions=[h],v.flags|=16):g.push(h)}}function n(v,h){if(!e)return null;for(;h!==null;)t(v,h),h=h.sibling;return null}function r(v,h){for(v=new Map;h!==null;)h.key!==null?v.set(h.key,h):v.set(h.index,h),h=h.sibling;return v}function o(v,h){return v=qt(v,h),v.index=0,v.sibling=null,v}function i(v,h,g){return v.index=g,e?(g=v.alternate,g!==null?(g=g.index,g<h?(v.flags|=2,h):g):(v.flags|=2,h)):(v.flags|=1048576,h)}function l(v){return e&&v.alternate===null&&(v.flags|=2),v}function s(v,h,g,E){return h===null||h.tag!==6?(h=Wl(g,v.mode,E),h.return=v,h):(h=o(h,g),h.return=v,h)}function a(v,h,g,E){var k=g.type;return k===An?f(v,h,g.props.children,E,g.key):h!==null&&(h.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===$t&&kc(k)===h.type)?(E=o(h,g.props),E.ref=Sr(v,h,g),E.return=v,E):(E=li(g.type,g.key,g.props,null,v.mode,E),E.ref=Sr(v,h,g),E.return=v,E)}function u(v,h,g,E){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=Vl(g,v.mode,E),h.return=v,h):(h=o(h,g.children||[]),h.return=v,h)}function f(v,h,g,E,k){return h===null||h.tag!==7?(h=hn(g,v.mode,E,k),h.return=v,h):(h=o(h,g),h.return=v,h)}function c(v,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=Wl(""+h,v.mode,g),h.return=v,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Po:return g=li(h.type,h.key,h.props,null,v.mode,g),g.ref=Sr(v,null,h),g.return=v,g;case Mn:return h=Vl(h,v.mode,g),h.return=v,h;case $t:var E=h._init;return c(v,E(h._payload),g)}if(Rr(h)||vr(h))return h=hn(h,v.mode,g,null),h.return=v,h;Do(v,h)}return null}function m(v,h,g,E){var k=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return k!==null?null:s(v,h,""+g,E);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Po:return g.key===k?a(v,h,g,E):null;case Mn:return g.key===k?u(v,h,g,E):null;case $t:return k=g._init,m(v,h,k(g._payload),E)}if(Rr(g)||vr(g))return k!==null?null:f(v,h,g,E,null);Do(v,g)}return null}function w(v,h,g,E,k){if(typeof E=="string"&&E!==""||typeof E=="number")return v=v.get(g)||null,s(h,v,""+E,k);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case Po:return v=v.get(E.key===null?g:E.key)||null,a(h,v,E,k);case Mn:return v=v.get(E.key===null?g:E.key)||null,u(h,v,E,k);case $t:var P=E._init;return w(v,h,g,P(E._payload),k)}if(Rr(E)||vr(E))return v=v.get(g)||null,f(h,v,E,k,null);Do(h,E)}return null}function x(v,h,g,E){for(var k=null,P=null,N=h,T=h=0,M=null;N!==null&&T<g.length;T++){N.index>T?(M=N,N=null):M=N.sibling;var _=m(v,N,g[T],E);if(_===null){N===null&&(N=M);break}e&&N&&_.alternate===null&&t(v,N),h=i(_,h,T),P===null?k=_:P.sibling=_,P=_,N=M}if(T===g.length)return n(v,N),te&&an(v,T),k;if(N===null){for(;T<g.length;T++)N=c(v,g[T],E),N!==null&&(h=i(N,h,T),P===null?k=N:P.sibling=N,P=N);return te&&an(v,T),k}for(N=r(v,N);T<g.length;T++)M=w(N,v,T,g[T],E),M!==null&&(e&&M.alternate!==null&&N.delete(M.key===null?T:M.key),h=i(M,h,T),P===null?k=M:P.sibling=M,P=M);return e&&N.forEach(function(L){return t(v,L)}),te&&an(v,T),k}function y(v,h,g,E){var k=vr(g);if(typeof k!="function")throw Error(b(150));if(g=k.call(g),g==null)throw Error(b(151));for(var P=k=null,N=h,T=h=0,M=null,_=g.next();N!==null&&!_.done;T++,_=g.next()){N.index>T?(M=N,N=null):M=N.sibling;var L=m(v,N,_.value,E);if(L===null){N===null&&(N=M);break}e&&N&&L.alternate===null&&t(v,N),h=i(L,h,T),P===null?k=L:P.sibling=L,P=L,N=M}if(_.done)return n(v,N),te&&an(v,T),k;if(N===null){for(;!_.done;T++,_=g.next())_=c(v,_.value,E),_!==null&&(h=i(_,h,T),P===null?k=_:P.sibling=_,P=_);return te&&an(v,T),k}for(N=r(v,N);!_.done;T++,_=g.next())_=w(N,v,T,_.value,E),_!==null&&(e&&_.alternate!==null&&N.delete(_.key===null?T:_.key),h=i(_,h,T),P===null?k=_:P.sibling=_,P=_);return e&&N.forEach(function(O){return t(v,O)}),te&&an(v,T),k}function S(v,h,g,E){if(typeof g=="object"&&g!==null&&g.type===An&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Po:e:{for(var k=g.key,P=h;P!==null;){if(P.key===k){if(k=g.type,k===An){if(P.tag===7){n(v,P.sibling),h=o(P,g.props.children),h.return=v,v=h;break e}}else if(P.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===$t&&kc(k)===P.type){n(v,P.sibling),h=o(P,g.props),h.ref=Sr(v,P,g),h.return=v,v=h;break e}n(v,P);break}else t(v,P);P=P.sibling}g.type===An?(h=hn(g.props.children,v.mode,E,g.key),h.return=v,v=h):(E=li(g.type,g.key,g.props,null,v.mode,E),E.ref=Sr(v,h,g),E.return=v,v=E)}return l(v);case Mn:e:{for(P=g.key;h!==null;){if(h.key===P)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){n(v,h.sibling),h=o(h,g.children||[]),h.return=v,v=h;break e}else{n(v,h);break}else t(v,h);h=h.sibling}h=Vl(g,v.mode,E),h.return=v,v=h}return l(v);case $t:return P=g._init,S(v,h,P(g._payload),E)}if(Rr(g))return x(v,h,g,E);if(vr(g))return y(v,h,g,E);Do(v,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(n(v,h.sibling),h=o(h,g),h.return=v,v=h):(n(v,h),h=Wl(g,v.mode,E),h.return=v,v=h),l(v)):n(v,h)}return S}var tr=Gf(!0),Yf=Gf(!1),Pi=ln(null),Ri=null,$n=null,Oa=null;function La(){Oa=$n=Ri=null}function Da(e){var t=Pi.current;ee(Pi),e._currentValue=t}function Os(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Gn(e,t){Ri=e,Oa=$n=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Me=!0),e.firstContext=null)}function Xe(e){var t=e._currentValue;if(Oa!==e)if(e={context:e,memoizedValue:t,next:null},$n===null){if(Ri===null)throw Error(b(308));$n=e,Ri.dependencies={lanes:0,firstContext:e}}else $n=$n.next=e;return t}var fn=null;function Fa(e){fn===null?fn=[e]:fn.push(e)}function Xf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Fa(t)):(n.next=o.next,o.next=n),t.interleaved=n,_t(e,r)}function _t(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ut=!1;function za(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Qf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Rt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Qt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,K&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,_t(e,n)}return o=r.interleaved,o===null?(t.next=t,Fa(r)):(t.next=o.next,o.next=t),r.interleaved=t,_t(e,n)}function ei(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ka(e,n)}}function Nc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ti(e,t,n,r){var o=e.updateQueue;Ut=!1;var i=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var f=e.alternate;f!==null&&(f=f.updateQueue,s=f.lastBaseUpdate,s!==l&&(s===null?f.firstBaseUpdate=u:s.next=u,f.lastBaseUpdate=a))}if(i!==null){var c=o.baseState;l=0,f=u=a=null,s=i;do{var m=s.lane,w=s.eventTime;if((r&m)===m){f!==null&&(f=f.next={eventTime:w,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var x=e,y=s;switch(m=t,w=n,y.tag){case 1:if(x=y.payload,typeof x=="function"){c=x.call(w,c,m);break e}c=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=y.payload,m=typeof x=="function"?x.call(w,c,m):x,m==null)break e;c=oe({},c,m);break e;case 2:Ut=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,m=o.effects,m===null?o.effects=[s]:m.push(s))}else w={eventTime:w,lane:m,tag:s.tag,payload:s.payload,callback:s.callback,next:null},f===null?(u=f=w,a=c):f=f.next=w,l|=m;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;m=s,s=m.next,m.next=null,o.lastBaseUpdate=m,o.shared.pending=null}}while(1);if(f===null&&(a=c),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);wn|=l,e.lanes=l,e.memoizedState=c}}function Pc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(b(191,o));o.call(r)}}}var go={},mt=ln(go),Jr=ln(go),qr=ln(go);function pn(e){if(e===go)throw Error(b(174));return e}function $a(e,t){switch(Q(qr,t),Q(Jr,e),Q(mt,go),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:hs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=hs(t,e)}ee(mt),Q(mt,t)}function nr(){ee(mt),ee(Jr),ee(qr)}function Zf(e){pn(qr.current);var t=pn(mt.current),n=hs(t,e.type);t!==n&&(Q(Jr,e),Q(mt,n))}function Ua(e){Jr.current===e&&(ee(mt),ee(Jr))}var ne=ln(0);function bi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ll=[];function Wa(){for(var e=0;e<Ll.length;e++)Ll[e]._workInProgressVersionPrimary=null;Ll.length=0}var ti=It.ReactCurrentDispatcher,Dl=It.ReactCurrentBatchConfig,yn=0,re=null,ce=null,fe=null,_i=!1,Lr=!1,eo=0,jy=0;function ye(){throw Error(b(321))}function Va(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ot(e[n],t[n]))return!1;return!0}function Ba(e,t,n,r,o,i){if(yn=i,re=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ti.current=e===null||e.memoizedState===null?Dy:Fy,e=n(r,o),Lr){i=0;do{if(Lr=!1,eo=0,25<=i)throw Error(b(301));i+=1,fe=ce=null,t.updateQueue=null,ti.current=zy,e=n(r,o)}while(Lr)}if(ti.current=Mi,t=ce!==null&&ce.next!==null,yn=0,fe=ce=re=null,_i=!1,t)throw Error(b(300));return e}function Ha(){var e=eo!==0;return eo=0,e}function at(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return fe===null?re.memoizedState=fe=e:fe=fe.next=e,fe}function Qe(){if(ce===null){var e=re.alternate;e=e!==null?e.memoizedState:null}else e=ce.next;var t=fe===null?re.memoizedState:fe.next;if(t!==null)fe=t,ce=e;else{if(e===null)throw Error(b(310));ce=e,e={memoizedState:ce.memoizedState,baseState:ce.baseState,baseQueue:ce.baseQueue,queue:ce.queue,next:null},fe===null?re.memoizedState=fe=e:fe=fe.next=e}return fe}function to(e,t){return typeof t=="function"?t(e):t}function Fl(e){var t=Qe(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=ce,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=l=null,a=null,u=i;do{var f=u.lane;if((yn&f)===f)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var c={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=c,l=r):a=a.next=c,re.lanes|=f,wn|=f}u=u.next}while(u!==null&&u!==i);a===null?l=r:a.next=s,ot(r,t.memoizedState)||(Me=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,re.lanes|=i,wn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function zl(e){var t=Qe(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do i=e(i,l.action),l=l.next;while(l!==o);ot(i,t.memoizedState)||(Me=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Jf(){}function qf(e,t){var n=re,r=Qe(),o=t(),i=!ot(r.memoizedState,o);if(i&&(r.memoizedState=o,Me=!0),r=r.queue,Ka(np.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||fe!==null&&fe.memoizedState.tag&1){if(n.flags|=2048,no(9,tp.bind(null,n,r,o,t),void 0,null),pe===null)throw Error(b(349));yn&30||ep(n,t,o)}return o}function ep(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=re.updateQueue,t===null?(t={lastEffect:null,stores:null},re.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function tp(e,t,n,r){t.value=n,t.getSnapshot=r,rp(t)&&op(e)}function np(e,t,n){return n(function(){rp(t)&&op(e)})}function rp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ot(e,n)}catch{return!0}}function op(e){var t=_t(e,1);t!==null&&rt(t,e,1,-1)}function Rc(e){var t=at();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:to,lastRenderedState:e},t.queue=e,e=e.dispatch=Ly.bind(null,re,e),[t.memoizedState,e]}function no(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=re.updateQueue,t===null?(t={lastEffect:null,stores:null},re.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ip(){return Qe().memoizedState}function ni(e,t,n,r){var o=at();re.flags|=e,o.memoizedState=no(1|t,n,void 0,r===void 0?null:r)}function Ji(e,t,n,r){var o=Qe();r=r===void 0?null:r;var i=void 0;if(ce!==null){var l=ce.memoizedState;if(i=l.destroy,r!==null&&Va(r,l.deps)){o.memoizedState=no(t,n,i,r);return}}re.flags|=e,o.memoizedState=no(1|t,n,i,r)}function Tc(e,t){return ni(8390656,8,e,t)}function Ka(e,t){return Ji(2048,8,e,t)}function lp(e,t){return Ji(4,2,e,t)}function sp(e,t){return Ji(4,4,e,t)}function ap(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function up(e,t,n){return n=n!=null?n.concat([e]):null,Ji(4,4,ap.bind(null,t,e),n)}function Ga(){}function cp(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Va(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function dp(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Va(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function fp(e,t,n){return yn&21?(ot(n,t)||(n=gf(),re.lanes|=n,wn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Me=!0),e.memoizedState=n)}function Iy(e,t){var n=G;G=n!==0&&4>n?n:4,e(!0);var r=Dl.transition;Dl.transition={};try{e(!1),t()}finally{G=n,Dl.transition=r}}function pp(){return Qe().memoizedState}function Oy(e,t,n){var r=Jt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},mp(e))hp(t,n);else if(n=Xf(e,t,n,r),n!==null){var o=Ne();rt(n,e,r,o),vp(n,t,r)}}function Ly(e,t,n){var r=Jt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(mp(e))hp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,n);if(o.hasEagerState=!0,o.eagerState=s,ot(s,l)){var a=t.interleaved;a===null?(o.next=o,Fa(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Xf(e,t,o,r),n!==null&&(o=Ne(),rt(n,e,r,o),vp(n,t,r))}}function mp(e){var t=e.alternate;return e===re||t!==null&&t===re}function hp(e,t){Lr=_i=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function vp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ka(e,n)}}var Mi={readContext:Xe,useCallback:ye,useContext:ye,useEffect:ye,useImperativeHandle:ye,useInsertionEffect:ye,useLayoutEffect:ye,useMemo:ye,useReducer:ye,useRef:ye,useState:ye,useDebugValue:ye,useDeferredValue:ye,useTransition:ye,useMutableSource:ye,useSyncExternalStore:ye,useId:ye,unstable_isNewReconciler:!1},Dy={readContext:Xe,useCallback:function(e,t){return at().memoizedState=[e,t===void 0?null:t],e},useContext:Xe,useEffect:Tc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ni(4194308,4,ap.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ni(4194308,4,e,t)},useInsertionEffect:function(e,t){return ni(4,2,e,t)},useMemo:function(e,t){var n=at();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=at();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Oy.bind(null,re,e),[r.memoizedState,e]},useRef:function(e){var t=at();return e={current:e},t.memoizedState=e},useState:Rc,useDebugValue:Ga,useDeferredValue:function(e){return at().memoizedState=e},useTransition:function(){var e=Rc(!1),t=e[0];return e=Iy.bind(null,e[1]),at().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=re,o=at();if(te){if(n===void 0)throw Error(b(407));n=n()}else{if(n=t(),pe===null)throw Error(b(349));yn&30||ep(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Tc(np.bind(null,r,i,e),[e]),r.flags|=2048,no(9,tp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=at(),t=pe.identifierPrefix;if(te){var n=Pt,r=Nt;n=(r&~(1<<32-nt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=eo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=jy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Fy={readContext:Xe,useCallback:cp,useContext:Xe,useEffect:Ka,useImperativeHandle:up,useInsertionEffect:lp,useLayoutEffect:sp,useMemo:dp,useReducer:Fl,useRef:ip,useState:function(){return Fl(to)},useDebugValue:Ga,useDeferredValue:function(e){var t=Qe();return fp(t,ce.memoizedState,e)},useTransition:function(){var e=Fl(to)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:Jf,useSyncExternalStore:qf,useId:pp,unstable_isNewReconciler:!1},zy={readContext:Xe,useCallback:cp,useContext:Xe,useEffect:Ka,useImperativeHandle:up,useInsertionEffect:lp,useLayoutEffect:sp,useMemo:dp,useReducer:zl,useRef:ip,useState:function(){return zl(to)},useDebugValue:Ga,useDeferredValue:function(e){var t=Qe();return ce===null?t.memoizedState=e:fp(t,ce.memoizedState,e)},useTransition:function(){var e=zl(to)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:Jf,useSyncExternalStore:qf,useId:pp,unstable_isNewReconciler:!1};function qe(e,t){if(e&&e.defaultProps){t=oe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ls(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:oe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var qi={isMounted:function(e){return(e=e._reactInternals)?Cn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ne(),o=Jt(e),i=Rt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Qt(e,i,o),t!==null&&(rt(t,e,o,r),ei(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ne(),o=Jt(e),i=Rt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Qt(e,i,o),t!==null&&(rt(t,e,o,r),ei(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ne(),r=Jt(e),o=Rt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Qt(e,o,r),t!==null&&(rt(t,e,r,n),ei(t,e,r))}};function bc(e,t,n,r,o,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!Yr(n,r)||!Yr(o,i):!0}function gp(e,t,n){var r=!1,o=tn,i=t.contextType;return typeof i=="object"&&i!==null?i=Xe(i):(o=je(t)?vn:Se.current,r=t.contextTypes,i=(r=r!=null)?qn(e,o):tn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=qi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function _c(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&qi.enqueueReplaceState(t,t.state,null)}function Ds(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},za(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Xe(i):(i=je(t)?vn:Se.current,o.context=qn(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ls(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&qi.enqueueReplaceState(o,o.state,null),Ti(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function rr(e,t){try{var n="",r=t;do n+=pg(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function $l(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Fs(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var $y=typeof WeakMap=="function"?WeakMap:Map;function yp(e,t,n){n=Rt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ji||(ji=!0,Ys=r),Fs(e,t)},n}function wp(e,t,n){n=Rt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Fs(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Fs(e,t),typeof r!="function"&&(Zt===null?Zt=new Set([this]):Zt.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function Mc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new $y;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=e0.bind(null,e,t,n),t.then(e,e))}function Ac(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function jc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Rt(-1,1),t.tag=2,Qt(n,t,1))),n.lanes|=1),e)}var Uy=It.ReactCurrentOwner,Me=!1;function ke(e,t,n,r){t.child=e===null?Yf(t,null,n,r):tr(t,e.child,n,r)}function Ic(e,t,n,r,o){n=n.render;var i=t.ref;return Gn(t,o),r=Ba(e,t,n,r,i,o),n=Ha(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Mt(e,t,o)):(te&&n&&Aa(t),t.flags|=1,ke(e,t,r,o),t.child)}function Oc(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!tu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,xp(e,t,i,r,o)):(e=li(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:Yr,n(l,r)&&e.ref===t.ref)return Mt(e,t,o)}return t.flags|=1,e=qt(i,r),e.ref=t.ref,e.return=t,t.child=e}function xp(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Yr(i,r)&&e.ref===t.ref)if(Me=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Me=!0);else return t.lanes=e.lanes,Mt(e,t,o)}return zs(e,t,n,r,o)}function Sp(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Q(Wn,Oe),Oe|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Q(Wn,Oe),Oe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Q(Wn,Oe),Oe|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Q(Wn,Oe),Oe|=r;return ke(e,t,o,n),t.child}function Ep(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function zs(e,t,n,r,o){var i=je(n)?vn:Se.current;return i=qn(t,i),Gn(t,o),n=Ba(e,t,n,r,i,o),r=Ha(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Mt(e,t,o)):(te&&r&&Aa(t),t.flags|=1,ke(e,t,n,o),t.child)}function Lc(e,t,n,r,o){if(je(n)){var i=!0;Ci(t)}else i=!1;if(Gn(t,o),t.stateNode===null)ri(e,t),gp(t,n,r),Ds(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=Xe(u):(u=je(n)?vn:Se.current,u=qn(t,u));var f=n.getDerivedStateFromProps,c=typeof f=="function"||typeof l.getSnapshotBeforeUpdate=="function";c||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||a!==u)&&_c(t,l,r,u),Ut=!1;var m=t.memoizedState;l.state=m,Ti(t,r,l,o),a=t.memoizedState,s!==r||m!==a||Ae.current||Ut?(typeof f=="function"&&(Ls(t,n,f,r),a=t.memoizedState),(s=Ut||bc(t,n,s,r,m,a,u))?(c||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Qf(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:qe(t.type,s),l.props=u,c=t.pendingProps,m=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=Xe(a):(a=je(n)?vn:Se.current,a=qn(t,a));var w=n.getDerivedStateFromProps;(f=typeof w=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==c||m!==a)&&_c(t,l,r,a),Ut=!1,m=t.memoizedState,l.state=m,Ti(t,r,l,o);var x=t.memoizedState;s!==c||m!==x||Ae.current||Ut?(typeof w=="function"&&(Ls(t,n,w,r),x=t.memoizedState),(u=Ut||bc(t,n,u,r,m,x,a)||!1)?(f||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,x,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,x,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),l.props=r,l.state=x,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return $s(e,t,n,r,i,o)}function $s(e,t,n,r,o,i){Ep(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&Sc(t,n,!1),Mt(e,t,i);r=t.stateNode,Uy.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=tr(t,e.child,null,i),t.child=tr(t,null,s,i)):ke(e,t,s,i),t.memoizedState=r.state,o&&Sc(t,n,!0),t.child}function Cp(e){var t=e.stateNode;t.pendingContext?xc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&xc(e,t.context,!1),$a(e,t.containerInfo)}function Dc(e,t,n,r,o){return er(),Ia(o),t.flags|=256,ke(e,t,n,r),t.child}var Us={dehydrated:null,treeContext:null,retryLane:0};function Ws(e){return{baseLanes:e,cachePool:null,transitions:null}}function kp(e,t,n){var r=t.pendingProps,o=ne.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Q(ne,o&1),e===null)return Is(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=nl(l,r,0,null),e=hn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ws(n),t.memoizedState=Us,e):Ya(t,l));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return Wy(e,t,l,r,s,o,n);if(i){i=r.fallback,l=t.mode,o=e.child,s=o.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=qt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=qt(s,i):(i=hn(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?Ws(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=Us,r}return i=e.child,e=i.sibling,r=qt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ya(e,t){return t=nl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Fo(e,t,n,r){return r!==null&&Ia(r),tr(t,e.child,null,n),e=Ya(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Wy(e,t,n,r,o,i,l){if(n)return t.flags&256?(t.flags&=-257,r=$l(Error(b(422))),Fo(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=nl({mode:"visible",children:r.children},o,0,null),i=hn(i,o,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&tr(t,e.child,null,l),t.child.memoizedState=Ws(l),t.memoizedState=Us,i);if(!(t.mode&1))return Fo(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(b(419)),r=$l(i,r,void 0),Fo(e,t,l,r)}if(s=(l&e.childLanes)!==0,Me||s){if(r=pe,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,_t(e,o),rt(r,e,o,-1))}return eu(),r=$l(Error(b(421))),Fo(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=t0.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,De=Xt(o.nextSibling),Fe=t,te=!0,tt=null,e!==null&&(Be[He++]=Nt,Be[He++]=Pt,Be[He++]=gn,Nt=e.id,Pt=e.overflow,gn=t),t=Ya(t,r.children),t.flags|=4096,t)}function Fc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Os(e.return,t,n)}function Ul(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Np(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(ke(e,t,r.children,n),r=ne.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Fc(e,n,t);else if(e.tag===19)Fc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Q(ne,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&bi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ul(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&bi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ul(t,!0,n,null,i);break;case"together":Ul(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ri(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Mt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),wn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(b(153));if(t.child!==null){for(e=t.child,n=qt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=qt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Vy(e,t,n){switch(t.tag){case 3:Cp(t),er();break;case 5:Zf(t);break;case 1:je(t.type)&&Ci(t);break;case 4:$a(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Q(Pi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Q(ne,ne.current&1),t.flags|=128,null):n&t.child.childLanes?kp(e,t,n):(Q(ne,ne.current&1),e=Mt(e,t,n),e!==null?e.sibling:null);Q(ne,ne.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Np(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Q(ne,ne.current),r)break;return null;case 22:case 23:return t.lanes=0,Sp(e,t,n)}return Mt(e,t,n)}var Pp,Vs,Rp,Tp;Pp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Vs=function(){};Rp=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,pn(mt.current);var i=null;switch(n){case"input":o=ds(e,o),r=ds(e,r),i=[];break;case"select":o=oe({},o,{value:void 0}),r=oe({},r,{value:void 0}),i=[];break;case"textarea":o=ms(e,o),r=ms(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Si)}vs(n,r);var l;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ur.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ur.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&q("scroll",e),i||s===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Tp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Er(e,t){if(!te)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function we(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function By(e,t,n){var r=t.pendingProps;switch(ja(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(t),null;case 1:return je(t.type)&&Ei(),we(t),null;case 3:return r=t.stateNode,nr(),ee(Ae),ee(Se),Wa(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Lo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,tt!==null&&(Zs(tt),tt=null))),Vs(e,t),we(t),null;case 5:Ua(t);var o=pn(qr.current);if(n=t.type,e!==null&&t.stateNode!=null)Rp(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(b(166));return we(t),null}if(e=pn(mt.current),Lo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[ut]=t,r[Zr]=i,e=(t.mode&1)!==0,n){case"dialog":q("cancel",r),q("close",r);break;case"iframe":case"object":case"embed":q("load",r);break;case"video":case"audio":for(o=0;o<br.length;o++)q(br[o],r);break;case"source":q("error",r);break;case"img":case"image":case"link":q("error",r),q("load",r);break;case"details":q("toggle",r);break;case"input":Gu(r,i),q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},q("invalid",r);break;case"textarea":Xu(r,i),q("invalid",r)}vs(n,i),o=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&Oo(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&Oo(r.textContent,s,e),o=["children",""+s]):Ur.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&q("scroll",r)}switch(n){case"input":Ro(r),Yu(r,i,!0);break;case"textarea":Ro(r),Qu(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Si)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=tf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[ut]=t,e[Zr]=r,Pp(e,t,!1,!1),t.stateNode=e;e:{switch(l=gs(n,r),n){case"dialog":q("cancel",e),q("close",e),o=r;break;case"iframe":case"object":case"embed":q("load",e),o=r;break;case"video":case"audio":for(o=0;o<br.length;o++)q(br[o],e);o=r;break;case"source":q("error",e),o=r;break;case"img":case"image":case"link":q("error",e),q("load",e),o=r;break;case"details":q("toggle",e),o=r;break;case"input":Gu(e,r),o=ds(e,r),q("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=oe({},r,{value:void 0}),q("invalid",e);break;case"textarea":Xu(e,r),o=ms(e,r),q("invalid",e);break;default:o=r}vs(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="style"?of(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&nf(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Wr(e,a):typeof a=="number"&&Wr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Ur.hasOwnProperty(i)?a!=null&&i==="onScroll"&&q("scroll",e):a!=null&&ya(e,i,a,l))}switch(n){case"input":Ro(e),Yu(e,r,!1);break;case"textarea":Ro(e),Qu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+en(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Vn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Vn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Si)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return we(t),null;case 6:if(e&&t.stateNode!=null)Tp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(b(166));if(n=pn(qr.current),pn(mt.current),Lo(t)){if(r=t.stateNode,n=t.memoizedProps,r[ut]=t,(i=r.nodeValue!==n)&&(e=Fe,e!==null))switch(e.tag){case 3:Oo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Oo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ut]=t,t.stateNode=r}return we(t),null;case 13:if(ee(ne),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(te&&De!==null&&t.mode&1&&!(t.flags&128))Kf(),er(),t.flags|=98560,i=!1;else if(i=Lo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(b(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(b(317));i[ut]=t}else er(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;we(t),i=!1}else tt!==null&&(Zs(tt),tt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ne.current&1?de===0&&(de=3):eu())),t.updateQueue!==null&&(t.flags|=4),we(t),null);case 4:return nr(),Vs(e,t),e===null&&Xr(t.stateNode.containerInfo),we(t),null;case 10:return Da(t.type._context),we(t),null;case 17:return je(t.type)&&Ei(),we(t),null;case 19:if(ee(ne),i=t.memoizedState,i===null)return we(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)Er(i,!1);else{if(de!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=bi(e),l!==null){for(t.flags|=128,Er(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Q(ne,ne.current&1|2),t.child}e=e.sibling}i.tail!==null&&se()>or&&(t.flags|=128,r=!0,Er(i,!1),t.lanes=4194304)}else{if(!r)if(e=bi(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Er(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!te)return we(t),null}else 2*se()-i.renderingStartTime>or&&n!==1073741824&&(t.flags|=128,r=!0,Er(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=se(),t.sibling=null,n=ne.current,Q(ne,r?n&1|2:n&1),t):(we(t),null);case 22:case 23:return qa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Oe&1073741824&&(we(t),t.subtreeFlags&6&&(t.flags|=8192)):we(t),null;case 24:return null;case 25:return null}throw Error(b(156,t.tag))}function Hy(e,t){switch(ja(t),t.tag){case 1:return je(t.type)&&Ei(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return nr(),ee(Ae),ee(Se),Wa(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ua(t),null;case 13:if(ee(ne),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(b(340));er()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ee(ne),null;case 4:return nr(),null;case 10:return Da(t.type._context),null;case 22:case 23:return qa(),null;case 24:return null;default:return null}}var zo=!1,xe=!1,Ky=typeof WeakSet=="function"?WeakSet:Set,I=null;function Un(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ie(e,t,r)}else n.current=null}function Bs(e,t,n){try{n()}catch(r){ie(e,t,r)}}var zc=!1;function Gy(e,t){if(Rs=yi,e=jf(),Ma(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,s=-1,a=-1,u=0,f=0,c=e,m=null;t:for(;;){for(var w;c!==n||o!==0&&c.nodeType!==3||(s=l+o),c!==i||r!==0&&c.nodeType!==3||(a=l+r),c.nodeType===3&&(l+=c.nodeValue.length),(w=c.firstChild)!==null;)m=c,c=w;for(;;){if(c===e)break t;if(m===n&&++u===o&&(s=l),m===i&&++f===r&&(a=l),(w=c.nextSibling)!==null)break;c=m,m=c.parentNode}c=w}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ts={focusedElem:e,selectionRange:n},yi=!1,I=t;I!==null;)if(t=I,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,I=e;else for(;I!==null;){t=I;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var y=x.memoizedProps,S=x.memoizedState,v=t.stateNode,h=v.getSnapshotBeforeUpdate(t.elementType===t.type?y:qe(t.type,y),S);v.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(b(163))}}catch(E){ie(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,I=e;break}I=t.return}return x=zc,zc=!1,x}function Dr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Bs(t,n,i)}o=o.next}while(o!==r)}}function el(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Hs(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function bp(e){var t=e.alternate;t!==null&&(e.alternate=null,bp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ut],delete t[Zr],delete t[Ms],delete t[by],delete t[_y])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function _p(e){return e.tag===5||e.tag===3||e.tag===4}function $c(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||_p(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ks(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Si));else if(r!==4&&(e=e.child,e!==null))for(Ks(e,t,n),e=e.sibling;e!==null;)Ks(e,t,n),e=e.sibling}function Gs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Gs(e,t,n),e=e.sibling;e!==null;)Gs(e,t,n),e=e.sibling}var me=null,et=!1;function Lt(e,t,n){for(n=n.child;n!==null;)Mp(e,t,n),n=n.sibling}function Mp(e,t,n){if(pt&&typeof pt.onCommitFiberUnmount=="function")try{pt.onCommitFiberUnmount(Ki,n)}catch{}switch(n.tag){case 5:xe||Un(n,t);case 6:var r=me,o=et;me=null,Lt(e,t,n),me=r,et=o,me!==null&&(et?(e=me,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):me.removeChild(n.stateNode));break;case 18:me!==null&&(et?(e=me,n=n.stateNode,e.nodeType===8?Il(e.parentNode,n):e.nodeType===1&&Il(e,n),Kr(e)):Il(me,n.stateNode));break;case 4:r=me,o=et,me=n.stateNode.containerInfo,et=!0,Lt(e,t,n),me=r,et=o;break;case 0:case 11:case 14:case 15:if(!xe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&Bs(n,t,l),o=o.next}while(o!==r)}Lt(e,t,n);break;case 1:if(!xe&&(Un(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){ie(n,t,s)}Lt(e,t,n);break;case 21:Lt(e,t,n);break;case 22:n.mode&1?(xe=(r=xe)||n.memoizedState!==null,Lt(e,t,n),xe=r):Lt(e,t,n);break;default:Lt(e,t,n)}}function Uc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Ky),t.forEach(function(r){var o=n0.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Je(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:me=s.stateNode,et=!1;break e;case 3:me=s.stateNode.containerInfo,et=!0;break e;case 4:me=s.stateNode.containerInfo,et=!0;break e}s=s.return}if(me===null)throw Error(b(160));Mp(i,l,o),me=null,et=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){ie(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Ap(t,e),t=t.sibling}function Ap(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Je(t,e),st(e),r&4){try{Dr(3,e,e.return),el(3,e)}catch(y){ie(e,e.return,y)}try{Dr(5,e,e.return)}catch(y){ie(e,e.return,y)}}break;case 1:Je(t,e),st(e),r&512&&n!==null&&Un(n,n.return);break;case 5:if(Je(t,e),st(e),r&512&&n!==null&&Un(n,n.return),e.flags&32){var o=e.stateNode;try{Wr(o,"")}catch(y){ie(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&qd(o,i),gs(s,l);var u=gs(s,i);for(l=0;l<a.length;l+=2){var f=a[l],c=a[l+1];f==="style"?of(o,c):f==="dangerouslySetInnerHTML"?nf(o,c):f==="children"?Wr(o,c):ya(o,f,c,u)}switch(s){case"input":fs(o,i);break;case"textarea":ef(o,i);break;case"select":var m=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var w=i.value;w!=null?Vn(o,!!i.multiple,w,!1):m!==!!i.multiple&&(i.defaultValue!=null?Vn(o,!!i.multiple,i.defaultValue,!0):Vn(o,!!i.multiple,i.multiple?[]:"",!1))}o[Zr]=i}catch(y){ie(e,e.return,y)}}break;case 6:if(Je(t,e),st(e),r&4){if(e.stateNode===null)throw Error(b(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){ie(e,e.return,y)}}break;case 3:if(Je(t,e),st(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Kr(t.containerInfo)}catch(y){ie(e,e.return,y)}break;case 4:Je(t,e),st(e);break;case 13:Je(t,e),st(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Za=se())),r&4&&Uc(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(xe=(u=xe)||f,Je(t,e),xe=u):Je(t,e),st(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(I=e,f=e.child;f!==null;){for(c=I=f;I!==null;){switch(m=I,w=m.child,m.tag){case 0:case 11:case 14:case 15:Dr(4,m,m.return);break;case 1:Un(m,m.return);var x=m.stateNode;if(typeof x.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(y){ie(r,n,y)}}break;case 5:Un(m,m.return);break;case 22:if(m.memoizedState!==null){Vc(c);continue}}w!==null?(w.return=m,I=w):Vc(c)}f=f.sibling}e:for(f=null,c=e;;){if(c.tag===5){if(f===null){f=c;try{o=c.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=c.stateNode,a=c.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=rf("display",l))}catch(y){ie(e,e.return,y)}}}else if(c.tag===6){if(f===null)try{c.stateNode.nodeValue=u?"":c.memoizedProps}catch(y){ie(e,e.return,y)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;f===c&&(f=null),c=c.return}f===c&&(f=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:Je(t,e),st(e),r&4&&Uc(e);break;case 21:break;default:Je(t,e),st(e)}}function st(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(_p(n)){var r=n;break e}n=n.return}throw Error(b(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Wr(o,""),r.flags&=-33);var i=$c(e);Gs(e,i,o);break;case 3:case 4:var l=r.stateNode.containerInfo,s=$c(e);Ks(e,s,l);break;default:throw Error(b(161))}}catch(a){ie(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Yy(e,t,n){I=e,jp(e)}function jp(e,t,n){for(var r=(e.mode&1)!==0;I!==null;){var o=I,i=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||zo;if(!l){var s=o.alternate,a=s!==null&&s.memoizedState!==null||xe;s=zo;var u=xe;if(zo=l,(xe=a)&&!u)for(I=o;I!==null;)l=I,a=l.child,l.tag===22&&l.memoizedState!==null?Bc(o):a!==null?(a.return=l,I=a):Bc(o);for(;i!==null;)I=i,jp(i),i=i.sibling;I=o,zo=s,xe=u}Wc(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,I=i):Wc(e)}}function Wc(e){for(;I!==null;){var t=I;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:xe||el(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!xe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:qe(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Pc(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Pc(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var c=f.dehydrated;c!==null&&Kr(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(b(163))}xe||t.flags&512&&Hs(t)}catch(m){ie(t,t.return,m)}}if(t===e){I=null;break}if(n=t.sibling,n!==null){n.return=t.return,I=n;break}I=t.return}}function Vc(e){for(;I!==null;){var t=I;if(t===e){I=null;break}var n=t.sibling;if(n!==null){n.return=t.return,I=n;break}I=t.return}}function Bc(e){for(;I!==null;){var t=I;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{el(4,t)}catch(a){ie(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){ie(t,o,a)}}var i=t.return;try{Hs(t)}catch(a){ie(t,i,a)}break;case 5:var l=t.return;try{Hs(t)}catch(a){ie(t,l,a)}}}catch(a){ie(t,t.return,a)}if(t===e){I=null;break}var s=t.sibling;if(s!==null){s.return=t.return,I=s;break}I=t.return}}var Xy=Math.ceil,Ai=It.ReactCurrentDispatcher,Xa=It.ReactCurrentOwner,Ge=It.ReactCurrentBatchConfig,K=0,pe=null,ae=null,he=0,Oe=0,Wn=ln(0),de=0,ro=null,wn=0,tl=0,Qa=0,Fr=null,_e=null,Za=0,or=1/0,Ct=null,ji=!1,Ys=null,Zt=null,$o=!1,Ht=null,Ii=0,zr=0,Xs=null,oi=-1,ii=0;function Ne(){return K&6?se():oi!==-1?oi:oi=se()}function Jt(e){return e.mode&1?K&2&&he!==0?he&-he:Ay.transition!==null?(ii===0&&(ii=gf()),ii):(e=G,e!==0||(e=window.event,e=e===void 0?16:kf(e.type)),e):1}function rt(e,t,n,r){if(50<zr)throw zr=0,Xs=null,Error(b(185));mo(e,n,r),(!(K&2)||e!==pe)&&(e===pe&&(!(K&2)&&(tl|=n),de===4&&Vt(e,he)),Ie(e,r),n===1&&K===0&&!(t.mode&1)&&(or=se()+500,Zi&&sn()))}function Ie(e,t){var n=e.callbackNode;Ag(e,t);var r=gi(e,e===pe?he:0);if(r===0)n!==null&&qu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&qu(n),t===1)e.tag===0?My(Hc.bind(null,e)):Vf(Hc.bind(null,e)),Ry(function(){!(K&6)&&sn()}),n=null;else{switch(yf(r)){case 1:n=Ca;break;case 4:n=hf;break;case 16:n=vi;break;case 536870912:n=vf;break;default:n=vi}n=Up(n,Ip.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Ip(e,t){if(oi=-1,ii=0,K&6)throw Error(b(327));var n=e.callbackNode;if(Yn()&&e.callbackNode!==n)return null;var r=gi(e,e===pe?he:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Oi(e,r);else{t=r;var o=K;K|=2;var i=Lp();(pe!==e||he!==t)&&(Ct=null,or=se()+500,mn(e,t));do try{Jy();break}catch(s){Op(e,s)}while(1);La(),Ai.current=i,K=o,ae!==null?t=0:(pe=null,he=0,t=de)}if(t!==0){if(t===2&&(o=Es(e),o!==0&&(r=o,t=Qs(e,o))),t===1)throw n=ro,mn(e,0),Vt(e,r),Ie(e,se()),n;if(t===6)Vt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Qy(o)&&(t=Oi(e,r),t===2&&(i=Es(e),i!==0&&(r=i,t=Qs(e,i))),t===1))throw n=ro,mn(e,0),Vt(e,r),Ie(e,se()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(b(345));case 2:un(e,_e,Ct);break;case 3:if(Vt(e,r),(r&130023424)===r&&(t=Za+500-se(),10<t)){if(gi(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ne(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=_s(un.bind(null,e,_e,Ct),t);break}un(e,_e,Ct);break;case 4:if(Vt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-nt(r);i=1<<l,l=t[l],l>o&&(o=l),r&=~i}if(r=o,r=se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Xy(r/1960))-r,10<r){e.timeoutHandle=_s(un.bind(null,e,_e,Ct),r);break}un(e,_e,Ct);break;case 5:un(e,_e,Ct);break;default:throw Error(b(329))}}}return Ie(e,se()),e.callbackNode===n?Ip.bind(null,e):null}function Qs(e,t){var n=Fr;return e.current.memoizedState.isDehydrated&&(mn(e,t).flags|=256),e=Oi(e,t),e!==2&&(t=_e,_e=n,t!==null&&Zs(t)),e}function Zs(e){_e===null?_e=e:_e.push.apply(_e,e)}function Qy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!ot(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Vt(e,t){for(t&=~Qa,t&=~tl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-nt(t),r=1<<n;e[n]=-1,t&=~r}}function Hc(e){if(K&6)throw Error(b(327));Yn();var t=gi(e,0);if(!(t&1))return Ie(e,se()),null;var n=Oi(e,t);if(e.tag!==0&&n===2){var r=Es(e);r!==0&&(t=r,n=Qs(e,r))}if(n===1)throw n=ro,mn(e,0),Vt(e,t),Ie(e,se()),n;if(n===6)throw Error(b(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,un(e,_e,Ct),Ie(e,se()),null}function Ja(e,t){var n=K;K|=1;try{return e(t)}finally{K=n,K===0&&(or=se()+500,Zi&&sn())}}function xn(e){Ht!==null&&Ht.tag===0&&!(K&6)&&Yn();var t=K;K|=1;var n=Ge.transition,r=G;try{if(Ge.transition=null,G=1,e)return e()}finally{G=r,Ge.transition=n,K=t,!(K&6)&&sn()}}function qa(){Oe=Wn.current,ee(Wn)}function mn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Py(n)),ae!==null)for(n=ae.return;n!==null;){var r=n;switch(ja(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ei();break;case 3:nr(),ee(Ae),ee(Se),Wa();break;case 5:Ua(r);break;case 4:nr();break;case 13:ee(ne);break;case 19:ee(ne);break;case 10:Da(r.type._context);break;case 22:case 23:qa()}n=n.return}if(pe=e,ae=e=qt(e.current,null),he=Oe=t,de=0,ro=null,Qa=tl=wn=0,_e=Fr=null,fn!==null){for(t=0;t<fn.length;t++)if(n=fn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=o,r.next=l}n.pending=r}fn=null}return e}function Op(e,t){do{var n=ae;try{if(La(),ti.current=Mi,_i){for(var r=re.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}_i=!1}if(yn=0,fe=ce=re=null,Lr=!1,eo=0,Xa.current=null,n===null||n.return===null){de=1,ro=t,ae=null;break}e:{var i=e,l=n.return,s=n,a=t;if(t=he,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,f=s,c=f.tag;if(!(f.mode&1)&&(c===0||c===11||c===15)){var m=f.alternate;m?(f.updateQueue=m.updateQueue,f.memoizedState=m.memoizedState,f.lanes=m.lanes):(f.updateQueue=null,f.memoizedState=null)}var w=Ac(l);if(w!==null){w.flags&=-257,jc(w,l,s,i,t),w.mode&1&&Mc(i,u,t),t=w,a=u;var x=t.updateQueue;if(x===null){var y=new Set;y.add(a),t.updateQueue=y}else x.add(a);break e}else{if(!(t&1)){Mc(i,u,t),eu();break e}a=Error(b(426))}}else if(te&&s.mode&1){var S=Ac(l);if(S!==null){!(S.flags&65536)&&(S.flags|=256),jc(S,l,s,i,t),Ia(rr(a,s));break e}}i=a=rr(a,s),de!==4&&(de=2),Fr===null?Fr=[i]:Fr.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var v=yp(i,a,t);Nc(i,v);break e;case 1:s=a;var h=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Zt===null||!Zt.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var E=wp(i,s,t);Nc(i,E);break e}}i=i.return}while(i!==null)}Fp(n)}catch(k){t=k,ae===n&&n!==null&&(ae=n=n.return);continue}break}while(1)}function Lp(){var e=Ai.current;return Ai.current=Mi,e===null?Mi:e}function eu(){(de===0||de===3||de===2)&&(de=4),pe===null||!(wn&268435455)&&!(tl&268435455)||Vt(pe,he)}function Oi(e,t){var n=K;K|=2;var r=Lp();(pe!==e||he!==t)&&(Ct=null,mn(e,t));do try{Zy();break}catch(o){Op(e,o)}while(1);if(La(),K=n,Ai.current=r,ae!==null)throw Error(b(261));return pe=null,he=0,de}function Zy(){for(;ae!==null;)Dp(ae)}function Jy(){for(;ae!==null&&!Cg();)Dp(ae)}function Dp(e){var t=$p(e.alternate,e,Oe);e.memoizedProps=e.pendingProps,t===null?Fp(e):ae=t,Xa.current=null}function Fp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Hy(n,t),n!==null){n.flags&=32767,ae=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{de=6,ae=null;return}}else if(n=By(n,t,Oe),n!==null){ae=n;return}if(t=t.sibling,t!==null){ae=t;return}ae=t=e}while(t!==null);de===0&&(de=5)}function un(e,t,n){var r=G,o=Ge.transition;try{Ge.transition=null,G=1,qy(e,t,n,r)}finally{Ge.transition=o,G=r}return null}function qy(e,t,n,r){do Yn();while(Ht!==null);if(K&6)throw Error(b(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(b(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(jg(e,i),e===pe&&(ae=pe=null,he=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||$o||($o=!0,Up(vi,function(){return Yn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ge.transition,Ge.transition=null;var l=G;G=1;var s=K;K|=4,Xa.current=null,Gy(e,n),Ap(n,e),wy(Ts),yi=!!Rs,Ts=Rs=null,e.current=n,Yy(n),kg(),K=s,G=l,Ge.transition=i}else e.current=n;if($o&&($o=!1,Ht=e,Ii=o),i=e.pendingLanes,i===0&&(Zt=null),Rg(n.stateNode),Ie(e,se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(ji)throw ji=!1,e=Ys,Ys=null,e;return Ii&1&&e.tag!==0&&Yn(),i=e.pendingLanes,i&1?e===Xs?zr++:(zr=0,Xs=e):zr=0,sn(),null}function Yn(){if(Ht!==null){var e=yf(Ii),t=Ge.transition,n=G;try{if(Ge.transition=null,G=16>e?16:e,Ht===null)var r=!1;else{if(e=Ht,Ht=null,Ii=0,K&6)throw Error(b(331));var o=K;for(K|=4,I=e.current;I!==null;){var i=I,l=i.child;if(I.flags&16){var s=i.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(I=u;I!==null;){var f=I;switch(f.tag){case 0:case 11:case 15:Dr(8,f,i)}var c=f.child;if(c!==null)c.return=f,I=c;else for(;I!==null;){f=I;var m=f.sibling,w=f.return;if(bp(f),f===u){I=null;break}if(m!==null){m.return=w,I=m;break}I=w}}}var x=i.alternate;if(x!==null){var y=x.child;if(y!==null){x.child=null;do{var S=y.sibling;y.sibling=null,y=S}while(y!==null)}}I=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,I=l;else e:for(;I!==null;){if(i=I,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Dr(9,i,i.return)}var v=i.sibling;if(v!==null){v.return=i.return,I=v;break e}I=i.return}}var h=e.current;for(I=h;I!==null;){l=I;var g=l.child;if(l.subtreeFlags&2064&&g!==null)g.return=l,I=g;else e:for(l=h;I!==null;){if(s=I,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:el(9,s)}}catch(k){ie(s,s.return,k)}if(s===l){I=null;break e}var E=s.sibling;if(E!==null){E.return=s.return,I=E;break e}I=s.return}}if(K=o,sn(),pt&&typeof pt.onPostCommitFiberRoot=="function")try{pt.onPostCommitFiberRoot(Ki,e)}catch{}r=!0}return r}finally{G=n,Ge.transition=t}}return!1}function Kc(e,t,n){t=rr(n,t),t=yp(e,t,1),e=Qt(e,t,1),t=Ne(),e!==null&&(mo(e,1,t),Ie(e,t))}function ie(e,t,n){if(e.tag===3)Kc(e,e,n);else for(;t!==null;){if(t.tag===3){Kc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Zt===null||!Zt.has(r))){e=rr(n,e),e=wp(t,e,1),t=Qt(t,e,1),e=Ne(),t!==null&&(mo(t,1,e),Ie(t,e));break}}t=t.return}}function e0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ne(),e.pingedLanes|=e.suspendedLanes&n,pe===e&&(he&n)===n&&(de===4||de===3&&(he&130023424)===he&&500>se()-Za?mn(e,0):Qa|=n),Ie(e,t)}function zp(e,t){t===0&&(e.mode&1?(t=_o,_o<<=1,!(_o&130023424)&&(_o=4194304)):t=1);var n=Ne();e=_t(e,t),e!==null&&(mo(e,t,n),Ie(e,n))}function t0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),zp(e,n)}function n0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(b(314))}r!==null&&r.delete(t),zp(e,n)}var $p;$p=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ae.current)Me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Me=!1,Vy(e,t,n);Me=!!(e.flags&131072)}else Me=!1,te&&t.flags&1048576&&Bf(t,Ni,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ri(e,t),e=t.pendingProps;var o=qn(t,Se.current);Gn(t,n),o=Ba(null,t,r,e,o,n);var i=Ha();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,je(r)?(i=!0,Ci(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,za(t),o.updater=qi,t.stateNode=o,o._reactInternals=t,Ds(t,r,e,n),t=$s(null,t,r,!0,i,n)):(t.tag=0,te&&i&&Aa(t),ke(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ri(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=o0(r),e=qe(r,e),o){case 0:t=zs(null,t,r,e,n);break e;case 1:t=Lc(null,t,r,e,n);break e;case 11:t=Ic(null,t,r,e,n);break e;case 14:t=Oc(null,t,r,qe(r.type,e),n);break e}throw Error(b(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:qe(r,o),zs(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:qe(r,o),Lc(e,t,r,o,n);case 3:e:{if(Cp(t),e===null)throw Error(b(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Qf(e,t),Ti(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=rr(Error(b(423)),t),t=Dc(e,t,r,n,o);break e}else if(r!==o){o=rr(Error(b(424)),t),t=Dc(e,t,r,n,o);break e}else for(De=Xt(t.stateNode.containerInfo.firstChild),Fe=t,te=!0,tt=null,n=Yf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(er(),r===o){t=Mt(e,t,n);break e}ke(e,t,r,n)}t=t.child}return t;case 5:return Zf(t),e===null&&Is(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,l=o.children,bs(r,o)?l=null:i!==null&&bs(r,i)&&(t.flags|=32),Ep(e,t),ke(e,t,l,n),t.child;case 6:return e===null&&Is(t),null;case 13:return kp(e,t,n);case 4:return $a(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=tr(t,null,r,n):ke(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:qe(r,o),Ic(e,t,r,o,n);case 7:return ke(e,t,t.pendingProps,n),t.child;case 8:return ke(e,t,t.pendingProps.children,n),t.child;case 12:return ke(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,Q(Pi,r._currentValue),r._currentValue=l,i!==null)if(ot(i.value,l)){if(i.children===o.children&&!Ae.current){t=Mt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=Rt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?a.next=a:(a.next=f.next,f.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Os(i.return,n,t),s.lanes|=n;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(b(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),Os(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}ke(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Gn(t,n),o=Xe(o),r=r(o),t.flags|=1,ke(e,t,r,n),t.child;case 14:return r=t.type,o=qe(r,t.pendingProps),o=qe(r.type,o),Oc(e,t,r,o,n);case 15:return xp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:qe(r,o),ri(e,t),t.tag=1,je(r)?(e=!0,Ci(t)):e=!1,Gn(t,n),gp(t,r,o),Ds(t,r,o,n),$s(null,t,r,!0,e,n);case 19:return Np(e,t,n);case 22:return Sp(e,t,n)}throw Error(b(156,t.tag))};function Up(e,t){return mf(e,t)}function r0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ke(e,t,n,r){return new r0(e,t,n,r)}function tu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function o0(e){if(typeof e=="function")return tu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===xa)return 11;if(e===Sa)return 14}return 2}function qt(e,t){var n=e.alternate;return n===null?(n=Ke(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function li(e,t,n,r,o,i){var l=2;if(r=e,typeof e=="function")tu(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case An:return hn(n.children,o,i,t);case wa:l=8,o|=8;break;case ss:return e=Ke(12,n,t,o|2),e.elementType=ss,e.lanes=i,e;case as:return e=Ke(13,n,t,o),e.elementType=as,e.lanes=i,e;case us:return e=Ke(19,n,t,o),e.elementType=us,e.lanes=i,e;case Qd:return nl(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Yd:l=10;break e;case Xd:l=9;break e;case xa:l=11;break e;case Sa:l=14;break e;case $t:l=16,r=null;break e}throw Error(b(130,e==null?e:typeof e,""))}return t=Ke(l,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function hn(e,t,n,r){return e=Ke(7,e,r,t),e.lanes=n,e}function nl(e,t,n,r){return e=Ke(22,e,r,t),e.elementType=Qd,e.lanes=n,e.stateNode={isHidden:!1},e}function Wl(e,t,n){return e=Ke(6,e,null,t),e.lanes=n,e}function Vl(e,t,n){return t=Ke(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function i0(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Cl(0),this.expirationTimes=Cl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Cl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function nu(e,t,n,r,o,i,l,s,a){return e=new i0(e,t,n,s,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ke(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},za(i),e}function l0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Mn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Wp(e){if(!e)return tn;e=e._reactInternals;e:{if(Cn(e)!==e||e.tag!==1)throw Error(b(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(b(171))}if(e.tag===1){var n=e.type;if(je(n))return Wf(e,n,t)}return t}function Vp(e,t,n,r,o,i,l,s,a){return e=nu(n,r,!0,e,o,i,l,s,a),e.context=Wp(null),n=e.current,r=Ne(),o=Jt(n),i=Rt(r,o),i.callback=t??null,Qt(n,i,o),e.current.lanes=o,mo(e,o,r),Ie(e,r),e}function rl(e,t,n,r){var o=t.current,i=Ne(),l=Jt(o);return n=Wp(n),t.context===null?t.context=n:t.pendingContext=n,t=Rt(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Qt(o,t,l),e!==null&&(rt(e,o,l,i),ei(e,o,l)),l}function Li(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Gc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ru(e,t){Gc(e,t),(e=e.alternate)&&Gc(e,t)}function s0(){return null}var Bp=typeof reportError=="function"?reportError:function(e){console.error(e)};function ou(e){this._internalRoot=e}ol.prototype.render=ou.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(b(409));rl(e,t,null,null)};ol.prototype.unmount=ou.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;xn(function(){rl(null,e,null,null)}),t[bt]=null}};function ol(e){this._internalRoot=e}ol.prototype.unstable_scheduleHydration=function(e){if(e){var t=Sf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Wt.length&&t!==0&&t<Wt[n].priority;n++);Wt.splice(n,0,e),n===0&&Cf(e)}};function iu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function il(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Yc(){}function a0(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Li(l);i.call(u)}}var l=Vp(t,r,e,0,null,!1,!1,"",Yc);return e._reactRootContainer=l,e[bt]=l.current,Xr(e.nodeType===8?e.parentNode:e),xn(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=Li(a);s.call(u)}}var a=nu(e,0,!1,null,null,!1,!1,"",Yc);return e._reactRootContainer=a,e[bt]=a.current,Xr(e.nodeType===8?e.parentNode:e),xn(function(){rl(t,a,n,r)}),a}function ll(e,t,n,r,o){var i=n._reactRootContainer;if(i){var l=i;if(typeof o=="function"){var s=o;o=function(){var a=Li(l);s.call(a)}}rl(t,l,e,o)}else l=a0(n,t,e,o,r);return Li(l)}wf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Tr(t.pendingLanes);n!==0&&(ka(t,n|1),Ie(t,se()),!(K&6)&&(or=se()+500,sn()))}break;case 13:xn(function(){var r=_t(e,1);if(r!==null){var o=Ne();rt(r,e,1,o)}}),ru(e,1)}};Na=function(e){if(e.tag===13){var t=_t(e,134217728);if(t!==null){var n=Ne();rt(t,e,134217728,n)}ru(e,134217728)}};xf=function(e){if(e.tag===13){var t=Jt(e),n=_t(e,t);if(n!==null){var r=Ne();rt(n,e,t,r)}ru(e,t)}};Sf=function(){return G};Ef=function(e,t){var n=G;try{return G=e,t()}finally{G=n}};ws=function(e,t,n){switch(t){case"input":if(fs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Qi(r);if(!o)throw Error(b(90));Jd(r),fs(r,o)}}}break;case"textarea":ef(e,n);break;case"select":t=n.value,t!=null&&Vn(e,!!n.multiple,t,!1)}};af=Ja;uf=xn;var u0={usingClientEntryPoint:!1,Events:[vo,Ln,Qi,lf,sf,Ja]},Cr={findFiberByHostInstance:dn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},c0={bundleType:Cr.bundleType,version:Cr.version,rendererPackageName:Cr.rendererPackageName,rendererConfig:Cr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:It.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ff(e),e===null?null:e.stateNode},findFiberByHostInstance:Cr.findFiberByHostInstance||s0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Uo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Uo.isDisabled&&Uo.supportsFiber)try{Ki=Uo.inject(c0),pt=Uo}catch{}}Ue.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=u0;Ue.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!iu(t))throw Error(b(200));return l0(e,t,null,n)};Ue.createRoot=function(e,t){if(!iu(e))throw Error(b(299));var n=!1,r="",o=Bp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=nu(e,1,!1,null,null,n,!1,r,o),e[bt]=t.current,Xr(e.nodeType===8?e.parentNode:e),new ou(t)};Ue.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(b(188)):(e=Object.keys(e).join(","),Error(b(268,e)));return e=ff(t),e=e===null?null:e.stateNode,e};Ue.flushSync=function(e){return xn(e)};Ue.hydrate=function(e,t,n){if(!il(t))throw Error(b(200));return ll(null,e,t,!0,n)};Ue.hydrateRoot=function(e,t,n){if(!iu(e))throw Error(b(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",l=Bp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=Vp(t,null,e,1,n??null,o,!1,i,l),e[bt]=t.current,Xr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ol(t)};Ue.render=function(e,t,n){if(!il(t))throw Error(b(200));return ll(null,e,t,!1,n)};Ue.unmountComponentAtNode=function(e){if(!il(e))throw Error(b(40));return e._reactRootContainer?(xn(function(){ll(null,null,e,!1,function(){e._reactRootContainer=null,e[bt]=null})}),!0):!1};Ue.unstable_batchedUpdates=Ja;Ue.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!il(n))throw Error(b(200));if(e==null||e._reactInternals===void 0)throw Error(b(38));return ll(e,t,n,!1,r)};Ue.version="18.3.1-next-f1338f8080-20240426";function Hp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Hp)}catch(e){console.error(e)}}Hp(),Bd.exports=Ue;var yo=Bd.exports;const d0=Md(yo);var Xc=yo;is.createRoot=Xc.createRoot,is.hydrateRoot=Xc.hydrateRoot;var f0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const p0=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),X=(e,t)=>{const n=p.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,children:s,...a},u)=>p.createElement("svg",{ref:u,...f0,width:o,height:o,stroke:r,strokeWidth:l?Number(i)*24/Number(o):i,className:`lucide lucide-${p0(e)}`,...a},[...t.map(([f,c])=>p.createElement(f,c)),...(Array.isArray(s)?s:[s])||[]]));return n.displayName=`${e}`,n},Js=X("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),Bl=X("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),m0=X("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),Kp=X("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),Gp=X("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),Yp=X("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),h0=X("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),Xp=X("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),Qp=X("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]),v0=X("FileJson",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M10 12a1 1 0 0 0-1 1v1a1 1 0 0 1-1 1 1 1 0 0 1 1 1v1a1 1 0 0 0 1 1",key:"1oajmo"}],["path",{d:"M14 18a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1 1 1 0 0 1-1-1v-1a1 1 0 0 0-1-1",key:"mpwhp6"}]]),oo=X("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),g0=X("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),y0=X("FolderPlus",[["path",{d:"M12 10v6",key:"1bos4e"}],["path",{d:"M9 13h6",key:"1uhe8q"}],["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),w0=X("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),Qc=X("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),x0=X("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),S0=X("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),E0=X("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]),C0=X("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),Zp=X("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Zc=X("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]),Jp=X("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]]),k0=X("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),N0=X("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),P0=X("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),R0=X("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),qs=X("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function T0(e){if(!e)return"";let t=e;return t=t.replace(/```(\w+)?\n([\s\S]*?)```/g,(n,r,o)=>`<pre class="bg-gray-800 border border-gray-600 rounded-lg p-4 overflow-x-auto my-3"><code class="language-${r||"text"} text-sm font-mono">${b0(o.trim())}</code></pre>`),t=t.replace(/`([^`]+)`/g,'<code class="bg-gray-700 px-2 py-1 rounded text-sm font-mono">$1</code>'),t=t.replace(/\*\*(.*?)\*\*/g,'<strong class="font-semibold">$1</strong>'),t=t.replace(/\*(.*?)\*/g,'<em class="italic">$1</em>'),t=t.replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" class="text-blue-400 hover:text-blue-300 underline" target="_blank" rel="noopener noreferrer">$1</a>'),t=t.replace(/(https?:\/\/[^\s]+)/g,'<a href="$1" class="text-blue-400 hover:text-blue-300 underline" target="_blank" rel="noopener noreferrer">$1</a>'),t=t.replace(/^### (.*$)/gm,'<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>'),t=t.replace(/^## (.*$)/gm,'<h2 class="text-xl font-semibold mt-4 mb-2">$1</h2>'),t=t.replace(/^# (.*$)/gm,'<h1 class="text-2xl font-bold mt-4 mb-2">$1</h1>'),t=t.replace(/^- (.*$)/gm,'<li class="ml-4">• $1</li>'),t=t.replace(/^(\d+)\. (.*$)/gm,'<li class="ml-4">$1. $2</li>'),t=t.replace(/(<li class="ml-4">.*<\/li>\s*)+/g,'<ul class="my-2">$&</ul>'),t=t.replace(/\n/g,"<br>"),t=t.replace(/<br>\s*(<pre|<h[1-6]|<ul)/g,"$1"),t=t.replace(/(<\/pre>|<\/h[1-6]>|<\/ul>)\s*<br>/g,"$1"),t}function b0(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}function qp(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=qp(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function em(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=qp(e))&&(r&&(r+=" "),r+=t);return r}const lu="-",_0=e=>{const t=A0(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:l=>{const s=l.split(lu);return s[0]===""&&s.length!==1&&s.shift(),tm(s,t)||M0(l)},getConflictingClassGroupIds:(l,s)=>{const a=n[l]||[];return s&&r[l]?[...a,...r[l]]:a}}},tm=(e,t)=>{var l;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?tm(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(lu);return(l=t.validators.find(({validator:s})=>s(i)))==null?void 0:l.classGroupId},Jc=/^\[(.+)\]$/,M0=e=>{if(Jc.test(e)){const t=Jc.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},A0=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return I0(Object.entries(e.classGroups),n).forEach(([i,l])=>{ea(l,r,i,t)}),r},ea=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:qc(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(j0(o)){ea(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,l])=>{ea(l,qc(t,i),n,r)})})},qc=(e,t)=>{let n=e;return t.split(lu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},j0=e=>e.isThemeGetter,I0=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([l,s])=>[t+l,s])):i);return[n,o]}):e,O0=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,l)=>{n.set(i,l),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let l=n.get(i);if(l!==void 0)return l;if((l=r.get(i))!==void 0)return o(i,l),l},set(i,l){n.has(i)?n.set(i,l):o(i,l)}}},nm="!",L0=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,l=s=>{const a=[];let u=0,f=0,c;for(let S=0;S<s.length;S++){let v=s[S];if(u===0){if(v===o&&(r||s.slice(S,S+i)===t)){a.push(s.slice(f,S)),f=S+i;continue}if(v==="/"){c=S;continue}}v==="["?u++:v==="]"&&u--}const m=a.length===0?s:s.substring(f),w=m.startsWith(nm),x=w?m.substring(1):m,y=c&&c>f?c-f:void 0;return{modifiers:a,hasImportantModifier:w,baseClassName:x,maybePostfixModifierPosition:y}};return n?s=>n({className:s,parseClassName:l}):l},D0=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},F0=e=>({cache:O0(e.cacheSize),parseClassName:L0(e),..._0(e)}),z0=/\s+/,$0=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],l=e.trim().split(z0);let s="";for(let a=l.length-1;a>=0;a-=1){const u=l[a],{modifiers:f,hasImportantModifier:c,baseClassName:m,maybePostfixModifierPosition:w}=n(u);let x=!!w,y=r(x?m.substring(0,w):m);if(!y){if(!x){s=u+(s.length>0?" "+s:s);continue}if(y=r(m),!y){s=u+(s.length>0?" "+s:s);continue}x=!1}const S=D0(f).join(":"),v=c?S+nm:S,h=v+y;if(i.includes(h))continue;i.push(h);const g=o(y,x);for(let E=0;E<g.length;++E){const k=g[E];i.push(v+k)}s=u+(s.length>0?" "+s:s)}return s};function U0(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=rm(t))&&(r&&(r+=" "),r+=n);return r}const rm=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=rm(e[r]))&&(n&&(n+=" "),n+=t);return n};function W0(e,...t){let n,r,o,i=l;function l(a){const u=t.reduce((f,c)=>c(f),e());return n=F0(u),r=n.cache.get,o=n.cache.set,i=s,s(a)}function s(a){const u=r(a);if(u)return u;const f=$0(a,n);return o(a,f),f}return function(){return i(U0.apply(null,arguments))}}const J=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},om=/^\[(?:([a-z-]+):)?(.+)\]$/i,V0=/^\d+\/\d+$/,B0=new Set(["px","full","screen"]),H0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,K0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,G0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Y0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,X0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,St=e=>Xn(e)||B0.has(e)||V0.test(e),Dt=e=>cr(e,"length",rw),Xn=e=>!!e&&!Number.isNaN(Number(e)),Hl=e=>cr(e,"number",Xn),kr=e=>!!e&&Number.isInteger(Number(e)),Q0=e=>e.endsWith("%")&&Xn(e.slice(0,-1)),$=e=>om.test(e),Ft=e=>H0.test(e),Z0=new Set(["length","size","percentage"]),J0=e=>cr(e,Z0,im),q0=e=>cr(e,"position",im),ew=new Set(["image","url"]),tw=e=>cr(e,ew,iw),nw=e=>cr(e,"",ow),Nr=()=>!0,cr=(e,t,n)=>{const r=om.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},rw=e=>K0.test(e)&&!G0.test(e),im=()=>!1,ow=e=>Y0.test(e),iw=e=>X0.test(e),lw=()=>{const e=J("colors"),t=J("spacing"),n=J("blur"),r=J("brightness"),o=J("borderColor"),i=J("borderRadius"),l=J("borderSpacing"),s=J("borderWidth"),a=J("contrast"),u=J("grayscale"),f=J("hueRotate"),c=J("invert"),m=J("gap"),w=J("gradientColorStops"),x=J("gradientColorStopPositions"),y=J("inset"),S=J("margin"),v=J("opacity"),h=J("padding"),g=J("saturate"),E=J("scale"),k=J("sepia"),P=J("skew"),N=J("space"),T=J("translate"),M=()=>["auto","contain","none"],_=()=>["auto","hidden","clip","visible","scroll"],L=()=>["auto",$,t],O=()=>[$,t],U=()=>["",St,Dt],j=()=>["auto",Xn,$],W=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],F=()=>["solid","dashed","dotted","double","none"],B=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],C=()=>["start","end","center","between","around","evenly","stretch"],R=()=>["","0",$],A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],z=()=>[Xn,$];return{cacheSize:500,separator:":",theme:{colors:[Nr],spacing:[St,Dt],blur:["none","",Ft,$],brightness:z(),borderColor:[e],borderRadius:["none","","full",Ft,$],borderSpacing:O(),borderWidth:U(),contrast:z(),grayscale:R(),hueRotate:z(),invert:R(),gap:O(),gradientColorStops:[e],gradientColorStopPositions:[Q0,Dt],inset:L(),margin:L(),opacity:z(),padding:O(),saturate:z(),scale:z(),sepia:R(),skew:z(),space:O(),translate:O()},classGroups:{aspect:[{aspect:["auto","square","video",$]}],container:["container"],columns:[{columns:[Ft]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...W(),$]}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",kr,$]}],basis:[{basis:L()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",$]}],grow:[{grow:R()}],shrink:[{shrink:R()}],order:[{order:["first","last","none",kr,$]}],"grid-cols":[{"grid-cols":[Nr]}],"col-start-end":[{col:["auto",{span:["full",kr,$]},$]}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":[Nr]}],"row-start-end":[{row:["auto",{span:[kr,$]},$]}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",$]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",$]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...C()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...C(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...C(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",$,t]}],"min-w":[{"min-w":[$,t,"min","max","fit"]}],"max-w":[{"max-w":[$,t,"none","full","min","max","fit","prose",{screen:[Ft]},Ft]}],h:[{h:[$,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[$,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[$,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[$,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ft,Dt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Hl]}],"font-family":[{font:[Nr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",$]}],"line-clamp":[{"line-clamp":["none",Xn,Hl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",St,$]}],"list-image":[{"list-image":["none",$]}],"list-style-type":[{list:["none","disc","decimal",$]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...F(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",St,Dt]}],"underline-offset":[{"underline-offset":["auto",St,$]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:O()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...W(),q0]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",J0]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},tw]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[w]}],"gradient-via":[{via:[w]}],"gradient-to":[{to:[w]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...F(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:F()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...F()]}],"outline-offset":[{"outline-offset":[St,$]}],"outline-w":[{outline:[St,Dt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[St,Dt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ft,nw]}],"shadow-color":[{shadow:[Nr]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...B(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":B()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",Ft,$]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[c]}],saturate:[{saturate:[g]}],sepia:[{sepia:[k]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",$]}],duration:[{duration:z()}],ease:[{ease:["linear","in","out","in-out",$]}],delay:[{delay:z()}],animate:[{animate:["none","spin","ping","pulse","bounce",$]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[E]}],"scale-x":[{"scale-x":[E]}],"scale-y":[{"scale-y":[E]}],rotate:[{rotate:[kr,$]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",$]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",$]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":O()}],"scroll-mx":[{"scroll-mx":O()}],"scroll-my":[{"scroll-my":O()}],"scroll-ms":[{"scroll-ms":O()}],"scroll-me":[{"scroll-me":O()}],"scroll-mt":[{"scroll-mt":O()}],"scroll-mr":[{"scroll-mr":O()}],"scroll-mb":[{"scroll-mb":O()}],"scroll-ml":[{"scroll-ml":O()}],"scroll-p":[{"scroll-p":O()}],"scroll-px":[{"scroll-px":O()}],"scroll-py":[{"scroll-py":O()}],"scroll-ps":[{"scroll-ps":O()}],"scroll-pe":[{"scroll-pe":O()}],"scroll-pt":[{"scroll-pt":O()}],"scroll-pr":[{"scroll-pr":O()}],"scroll-pb":[{"scroll-pb":O()}],"scroll-pl":[{"scroll-pl":O()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",$]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[St,Dt,Hl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},sw=W0(lw);function Z(...e){return sw(em(e))}const io=p.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,className:Z("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));io.displayName="Card";const lm=p.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,className:Z("flex flex-col space-y-1.5 p-6",e),...t}));lm.displayName="CardHeader";const sm=p.forwardRef(({className:e,...t},n)=>d.jsx("h3",{ref:n,className:Z("text-2xl font-semibold leading-none tracking-tight",e),...t}));sm.displayName="CardTitle";const aw=p.forwardRef(({className:e,...t},n)=>d.jsx("p",{ref:n,className:Z("text-sm text-muted-foreground",e),...t}));aw.displayName="CardDescription";const lo=p.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,className:Z("p-6 pt-0",e),...t}));lo.displayName="CardContent";const uw=p.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,className:Z("flex items-center p-6 pt-0",e),...t}));uw.displayName="CardFooter";const ed=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,td=em,su=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return td(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,l=Object.keys(o).map(u=>{const f=n==null?void 0:n[u],c=i==null?void 0:i[u];if(f===null)return null;const m=ed(f)||ed(c);return o[u][m]}),s=n&&Object.entries(n).reduce((u,f)=>{let[c,m]=f;return m===void 0||(u[c]=m),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,f)=>{let{class:c,className:m,...w}=f;return Object.entries(w).every(x=>{let[y,S]=x;return Array.isArray(S)?S.includes({...i,...s}[y]):{...i,...s}[y]===S})?[...u,c,m]:u},[]);return td(e,l,a,n==null?void 0:n.class,n==null?void 0:n.className)},cw=su("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function ct({className:e,variant:t,...n}){return d.jsx("div",{className:Z(cw({variant:t}),e),...n})}const dw=({message:e})=>{const{type:t,content:n,timestamp:r}=e,o=()=>{switch(t){case"user":return d.jsx(N0,{className:"w-4 h-4"});case"ai":return d.jsx(Js,{className:"w-4 h-4"});case"system":return d.jsx(S0,{className:"w-4 h-4"});default:return null}},i=()=>{switch(t){case"user":return"justify-end";case"system":return"justify-center";default:return"justify-start"}},l=()=>{switch(t){case"user":return"bg-primary text-primary-foreground";case"ai":return"bg-card border";case"system":return"bg-muted border-muted";default:return"bg-card border"}},s=u=>new Intl.DateTimeFormat("en-US",{hour:"2-digit",minute:"2-digit",hour12:!1}).format(u),a=()=>t==="system"?"max-w-md":"max-w-3xl";return d.jsx("div",{className:`flex ${i()}`,children:d.jsx(io,{className:`${a()} ${l()}`,children:d.jsxs(lo,{className:"p-4",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[d.jsx("div",{className:`p-1.5 rounded-full ${t==="user"?"bg-primary-foreground/20":t==="ai"?"bg-primary/10":"bg-muted-foreground/10"}`,children:o()}),d.jsx(ct,{variant:t==="user"?"secondary":"outline",className:"text-xs",children:t==="user"?"You":t==="ai"?"ChatProj":"System"}),d.jsxs("div",{className:"flex items-center gap-1 ml-auto text-xs text-muted-foreground",children:[d.jsx(Xp,{className:"w-3 h-3"}),s(r)]})]}),d.jsx("div",{className:`prose prose-sm max-w-none ${t==="user"?"prose-invert":"prose-slate dark:prose-invert"}`,dangerouslySetInnerHTML:{__html:T0(n)}})]})})})},fw=({tree:e,onFileSelect:t,selectedFile:n})=>{const[r,o]=p.useState(new Set([""])),i=u=>{o(f=>{const c=new Set(f);return c.has(u)?c.delete(u):c.add(u),c})},l=u=>{var c;if(u.type==="directory")return r.has(u.path)?d.jsx(g0,{className:"w-4 h-4 text-blue-400"}):d.jsx(w0,{className:"w-4 h-4 text-blue-400"});switch((c=u.extension)==null?void 0:c.toLowerCase()){case".js":case".jsx":case".ts":case".tsx":case".vue":case".svelte":return d.jsx(Qp,{className:"w-4 h-4 text-yellow-400"});case".json":return d.jsx(v0,{className:"w-4 h-4 text-green-400"});case".png":case".jpg":case".jpeg":case".gif":case".svg":case".webp":return d.jsx(x0,{className:"w-4 h-4 text-purple-400"});case".md":case".txt":return d.jsx(oo,{className:"w-4 h-4 text-gray-400"});case".env":case".gitignore":case".eslintrc":case".prettierrc":return d.jsx(Zp,{className:"w-4 h-4 text-orange-400"});default:return d.jsx(oo,{className:"w-4 h-4 text-gray-400"})}},s=(u,f=0)=>{if(!u)return null;const c=r.has(u.path),m=(n==null?void 0:n.path)===u.path,w=u.type==="directory";return d.jsxs("div",{children:[d.jsxs("div",{className:`
            flex items-center gap-2 py-1 px-2 rounded cursor-pointer
            hover:bg-gray-700 transition-colors
            ${m?"bg-blue-600 text-white":""}
          `,style:{paddingLeft:`${f*16+8}px`},onClick:()=>{w?i(u.path):t==null||t(u)},children:[w&&d.jsx("div",{className:"w-4 h-4 flex items-center justify-center",children:c?d.jsx(Gp,{className:"w-3 h-3 text-gray-400"}):d.jsx(Yp,{className:"w-3 h-3 text-gray-400"})}),l(u),d.jsx("span",{className:"text-sm truncate flex-1",children:u.name}),!w&&u.size&&d.jsx("span",{className:"text-xs text-gray-500",children:a(u.size)})]}),w&&c&&u.children&&d.jsx("div",{children:u.children.map(x=>s(x,f+1))})]},u.path)},a=u=>{if(u===0)return"0 B";const f=1024,c=["B","KB","MB","GB"],m=Math.floor(Math.log(u)/Math.log(f));return parseFloat((u/Math.pow(f,m)).toFixed(1))+" "+c[m]};return e?d.jsx("div",{className:"text-sm",children:s(e)}):d.jsx("div",{className:"text-sm text-gray-500 p-2",children:"No files found"})},pw=({action:e})=>{const{type:t,payload:n,status:r,error:o,result:i,timestamp:l}=e,s=()=>{switch(t){case"CREATE_FILE":case"UPDATE_FILE":return d.jsx(oo,{className:"w-4 h-4"});case"DELETE_FILE":return d.jsx(k0,{className:"w-4 h-4"});case"CREATE_DIRECTORY":return d.jsx(y0,{className:"w-4 h-4"});case"RUN_COMMAND":return d.jsx(Jp,{className:"w-4 h-4"});case"INSTALL_PACKAGE":return d.jsx(E0,{className:"w-4 h-4"});default:return d.jsx(oo,{className:"w-4 h-4"})}},a=()=>{switch(r){case"success":return d.jsx(m0,{className:"w-4 h-4 text-green-500"});case"error":return d.jsx(P0,{className:"w-4 h-4 text-red-500"});case"pending":default:return d.jsx(Xp,{className:"w-4 h-4 text-yellow-500"})}},u=()=>{switch(r){case"success":return"border-green-500 bg-green-900/20";case"error":return"border-red-500 bg-red-900/20";case"pending":default:return"border-yellow-500 bg-yellow-900/20"}},f=()=>{switch(t){case"CREATE_FILE":return`Create file: ${n.path}`;case"UPDATE_FILE":return`Update file: ${n.path}`;case"DELETE_FILE":return`Delete file: ${n.path}`;case"CREATE_DIRECTORY":return`Create directory: ${n.path}`;case"RUN_COMMAND":return`Run command: ${n.command}`;case"INSTALL_PACKAGE":return`Install package: ${n.package}`;default:return`${t}: ${JSON.stringify(n)}`}},c=m=>new Intl.DateTimeFormat("en-US",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(m);return d.jsx(io,{className:`${u()}`,children:d.jsx(lo,{className:"p-3",children:d.jsxs("div",{className:"flex items-start gap-3",children:[d.jsx("div",{className:"flex-shrink-0 mt-0.5",children:s()}),d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[d.jsx("span",{className:"text-sm font-medium",children:f()}),a()]}),d.jsx("div",{className:"text-xs text-muted-foreground",children:c(l)}),r==="error"&&o&&d.jsx("div",{className:"mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded",children:d.jsxs("div",{className:"text-sm text-destructive",children:[d.jsx("strong",{children:"Error:"})," ",o]})}),r==="success"&&i&&d.jsxs("div",{className:"mt-2 space-y-2",children:[i.stdout&&d.jsxs("div",{className:"p-2 bg-green-500/10 border border-green-500/20 rounded",children:[d.jsx("div",{className:"text-sm text-green-700 dark:text-green-300",children:d.jsx("strong",{children:"Output:"})}),d.jsx("pre",{className:"mt-1 text-xs whitespace-pre-wrap font-mono",children:i.stdout})]}),i.stderr&&d.jsxs("div",{className:"p-2 bg-yellow-500/10 border border-yellow-500/20 rounded",children:[d.jsx("div",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:d.jsx("strong",{children:"Warnings:"})}),d.jsx("pre",{className:"mt-1 text-xs whitespace-pre-wrap font-mono",children:i.stderr})]})]}),r==="pending"&&d.jsx("div",{className:"mt-2 flex items-center gap-2",children:d.jsxs(ct,{variant:"secondary",className:"animate-pulse",children:[d.jsx("div",{className:"w-2 h-2 bg-current rounded-full mr-1"}),"Executing..."]})})]})]})})})};function nd(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function sl(...e){return t=>{let n=!1;const r=e.map(o=>{const i=nd(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():nd(e[o],null)}}}}function le(...e){return p.useCallback(sl(...e),e)}function so(e){const t=hw(e),n=p.forwardRef((r,o)=>{const{children:i,...l}=r,s=p.Children.toArray(i),a=s.find(gw);if(a){const u=a.props.children,f=s.map(c=>c===a?p.Children.count(u)>1?p.Children.only(null):p.isValidElement(u)?u.props.children:null:c);return d.jsx(t,{...l,ref:o,children:p.isValidElement(u)?p.cloneElement(u,void 0,f):null})}return d.jsx(t,{...l,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}var mw=so("Slot");function hw(e){const t=p.forwardRef((n,r)=>{const{children:o,...i}=n;if(p.isValidElement(o)){const l=ww(o),s=yw(i,o.props);return o.type!==p.Fragment&&(s.ref=r?sl(r,l):l),p.cloneElement(o,s)}return p.Children.count(o)>1?p.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var vw=Symbol("radix.slottable");function gw(e){return p.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===vw}function yw(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...s)=>{const a=i(...s);return o(...s),a}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function ww(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const xw=su("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),cn=p.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{const l=r?mw:"button";return d.jsx(l,{className:Z(xw({variant:t,size:n,className:e})),ref:i,...o})});cn.displayName="Button";function D(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function kn(e,t=[]){let n=[];function r(i,l){const s=p.createContext(l),a=n.length;n=[...n,l];const u=c=>{var v;const{scope:m,children:w,...x}=c,y=((v=m==null?void 0:m[e])==null?void 0:v[a])||s,S=p.useMemo(()=>x,Object.values(x));return d.jsx(y.Provider,{value:S,children:w})};u.displayName=i+"Provider";function f(c,m){var y;const w=((y=m==null?void 0:m[e])==null?void 0:y[a])||s,x=p.useContext(w);if(x)return x;if(l!==void 0)return l;throw new Error(`\`${c}\` must be used within \`${i}\``)}return[u,f]}const o=()=>{const i=n.map(l=>p.createContext(l));return function(s){const a=(s==null?void 0:s[e])||i;return p.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return o.scopeName=e,[r,Sw(o,...t)]}function Sw(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const l=r.reduce((s,{useScope:a,scopeName:u})=>{const c=a(i)[`__scope${u}`];return{...s,...c}},{});return p.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}var vt=globalThis!=null&&globalThis.document?p.useLayoutEffect:()=>{},Ew=Wd[" useInsertionEffect ".trim().toString()]||vt;function au({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,l]=Cw({defaultProp:t,onChange:n}),s=e!==void 0,a=s?e:o;{const f=p.useRef(e!==void 0);p.useEffect(()=>{const c=f.current;c!==s&&console.warn(`${r} is changing from ${c?"controlled":"uncontrolled"} to ${s?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=s},[s,r])}const u=p.useCallback(f=>{var c;if(s){const m=kw(f)?f(e):f;m!==e&&((c=l.current)==null||c.call(l,m))}else i(f)},[s,e,i,l]);return[a,u]}function Cw({defaultProp:e,onChange:t}){const[n,r]=p.useState(e),o=p.useRef(n),i=p.useRef(t);return Ew(()=>{i.current=t},[t]),p.useEffect(()=>{var l;o.current!==n&&((l=i.current)==null||l.call(i,n),o.current=n)},[n,o]),[n,r,i]}function kw(e){return typeof e=="function"}var Nw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Y=Nw.reduce((e,t)=>{const n=so(`Primitive.${t}`),r=p.forwardRef((o,i)=>{const{asChild:l,...s}=o,a=l?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),d.jsx(a,{...s,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function uu(e,t){e&&yo.flushSync(()=>e.dispatchEvent(t))}function cu(e){const t=e+"CollectionProvider",[n,r]=kn(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=y=>{const{scope:S,children:v}=y,h=Et.useRef(null),g=Et.useRef(new Map).current;return d.jsx(o,{scope:S,itemMap:g,collectionRef:h,children:v})};l.displayName=t;const s=e+"CollectionSlot",a=so(s),u=Et.forwardRef((y,S)=>{const{scope:v,children:h}=y,g=i(s,v),E=le(S,g.collectionRef);return d.jsx(a,{ref:E,children:h})});u.displayName=s;const f=e+"CollectionItemSlot",c="data-radix-collection-item",m=so(f),w=Et.forwardRef((y,S)=>{const{scope:v,children:h,...g}=y,E=Et.useRef(null),k=le(S,E),P=i(f,v);return Et.useEffect(()=>(P.itemMap.set(E,{ref:E,...g}),()=>void P.itemMap.delete(E))),d.jsx(m,{[c]:"",ref:k,children:h})});w.displayName=f;function x(y){const S=i(e+"CollectionConsumer",y);return Et.useCallback(()=>{const h=S.collectionRef.current;if(!h)return[];const g=Array.from(h.querySelectorAll(`[${c}]`));return Array.from(S.itemMap.values()).sort((P,N)=>g.indexOf(P.ref.current)-g.indexOf(N.ref.current))},[S.collectionRef,S.itemMap])}return[{Provider:l,Slot:u,ItemSlot:w},x,r]}var Pw=p.createContext(void 0);function du(e){const t=p.useContext(Pw);return e||t||"ltr"}function ue(e){const t=p.useRef(e);return p.useEffect(()=>{t.current=e}),p.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Rw(e,t=globalThis==null?void 0:globalThis.document){const n=ue(e);p.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Tw="DismissableLayer",ta="dismissableLayer.update",bw="dismissableLayer.pointerDownOutside",_w="dismissableLayer.focusOutside",rd,am=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),fu=p.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:l,onDismiss:s,...a}=e,u=p.useContext(am),[f,c]=p.useState(null),m=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,w]=p.useState({}),x=le(t,N=>c(N)),y=Array.from(u.layers),[S]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),v=y.indexOf(S),h=f?y.indexOf(f):-1,g=u.layersWithOutsidePointerEventsDisabled.size>0,E=h>=v,k=Aw(N=>{const T=N.target,M=[...u.branches].some(_=>_.contains(T));!E||M||(o==null||o(N),l==null||l(N),N.defaultPrevented||s==null||s())},m),P=jw(N=>{const T=N.target;[...u.branches].some(_=>_.contains(T))||(i==null||i(N),l==null||l(N),N.defaultPrevented||s==null||s())},m);return Rw(N=>{h===u.layers.size-1&&(r==null||r(N),!N.defaultPrevented&&s&&(N.preventDefault(),s()))},m),p.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(rd=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),od(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=rd)}},[f,m,n,u]),p.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),od())},[f,u]),p.useEffect(()=>{const N=()=>w({});return document.addEventListener(ta,N),()=>document.removeEventListener(ta,N)},[]),d.jsx(Y.div,{...a,ref:x,style:{pointerEvents:g?E?"auto":"none":void 0,...e.style},onFocusCapture:D(e.onFocusCapture,P.onFocusCapture),onBlurCapture:D(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:D(e.onPointerDownCapture,k.onPointerDownCapture)})});fu.displayName=Tw;var Mw="DismissableLayerBranch",um=p.forwardRef((e,t)=>{const n=p.useContext(am),r=p.useRef(null),o=le(t,r);return p.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),d.jsx(Y.div,{...e,ref:o})});um.displayName=Mw;function Aw(e,t=globalThis==null?void 0:globalThis.document){const n=ue(e),r=p.useRef(!1),o=p.useRef(()=>{});return p.useEffect(()=>{const i=s=>{if(s.target&&!r.current){let a=function(){cm(bw,n,u,{discrete:!0})};const u={originalEvent:s};s.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(l),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function jw(e,t=globalThis==null?void 0:globalThis.document){const n=ue(e),r=p.useRef(!1);return p.useEffect(()=>{const o=i=>{i.target&&!r.current&&cm(_w,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function od(){const e=new CustomEvent(ta);document.dispatchEvent(e)}function cm(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?uu(o,i):o.dispatchEvent(i)}var Iw=fu,Ow=um,Kl=0;function Lw(){p.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??id()),document.body.insertAdjacentElement("beforeend",e[1]??id()),Kl++,()=>{Kl===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Kl--}},[])}function id(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Gl="focusScope.autoFocusOnMount",Yl="focusScope.autoFocusOnUnmount",ld={bubbles:!1,cancelable:!0},Dw="FocusScope",dm=p.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[s,a]=p.useState(null),u=ue(o),f=ue(i),c=p.useRef(null),m=le(t,y=>a(y)),w=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(r){let y=function(g){if(w.paused||!s)return;const E=g.target;s.contains(E)?c.current=E:zt(c.current,{select:!0})},S=function(g){if(w.paused||!s)return;const E=g.relatedTarget;E!==null&&(s.contains(E)||zt(c.current,{select:!0}))},v=function(g){if(document.activeElement===document.body)for(const k of g)k.removedNodes.length>0&&zt(s)};document.addEventListener("focusin",y),document.addEventListener("focusout",S);const h=new MutationObserver(v);return s&&h.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",y),document.removeEventListener("focusout",S),h.disconnect()}}},[r,s,w.paused]),p.useEffect(()=>{if(s){ad.add(w);const y=document.activeElement;if(!s.contains(y)){const v=new CustomEvent(Gl,ld);s.addEventListener(Gl,u),s.dispatchEvent(v),v.defaultPrevented||(Fw(Vw(fm(s)),{select:!0}),document.activeElement===y&&zt(s))}return()=>{s.removeEventListener(Gl,u),setTimeout(()=>{const v=new CustomEvent(Yl,ld);s.addEventListener(Yl,f),s.dispatchEvent(v),v.defaultPrevented||zt(y??document.body,{select:!0}),s.removeEventListener(Yl,f),ad.remove(w)},0)}}},[s,u,f,w]);const x=p.useCallback(y=>{if(!n&&!r||w.paused)return;const S=y.key==="Tab"&&!y.altKey&&!y.ctrlKey&&!y.metaKey,v=document.activeElement;if(S&&v){const h=y.currentTarget,[g,E]=zw(h);g&&E?!y.shiftKey&&v===E?(y.preventDefault(),n&&zt(g,{select:!0})):y.shiftKey&&v===g&&(y.preventDefault(),n&&zt(E,{select:!0})):v===h&&y.preventDefault()}},[n,r,w.paused]);return d.jsx(Y.div,{tabIndex:-1,...l,ref:m,onKeyDown:x})});dm.displayName=Dw;function Fw(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(zt(r,{select:t}),document.activeElement!==n)return}function zw(e){const t=fm(e),n=sd(t,e),r=sd(t.reverse(),e);return[n,r]}function fm(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function sd(e,t){for(const n of e)if(!$w(n,{upTo:t}))return n}function $w(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Uw(e){return e instanceof HTMLInputElement&&"select"in e}function zt(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Uw(e)&&t&&e.select()}}var ad=Ww();function Ww(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=ud(e,t),e.unshift(t)},remove(t){var n;e=ud(e,t),(n=e[0])==null||n.resume()}}}function ud(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Vw(e){return e.filter(t=>t.tagName!=="A")}var Bw=Wd[" useId ".trim().toString()]||(()=>{}),Hw=0;function na(e){const[t,n]=p.useState(Bw());return vt(()=>{e||n(r=>r??String(Hw++))},[e]),e||(t?`radix-${t}`:"")}const Kw=["top","right","bottom","left"],nn=Math.min,Le=Math.max,Di=Math.round,Wo=Math.floor,ht=e=>({x:e,y:e}),Gw={left:"right",right:"left",bottom:"top",top:"bottom"},Yw={start:"end",end:"start"};function ra(e,t,n){return Le(e,nn(t,n))}function At(e,t){return typeof e=="function"?e(t):e}function jt(e){return e.split("-")[0]}function dr(e){return e.split("-")[1]}function pu(e){return e==="x"?"y":"x"}function mu(e){return e==="y"?"height":"width"}const Xw=new Set(["top","bottom"]);function ft(e){return Xw.has(jt(e))?"y":"x"}function hu(e){return pu(ft(e))}function Qw(e,t,n){n===void 0&&(n=!1);const r=dr(e),o=hu(e),i=mu(o);let l=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=Fi(l)),[l,Fi(l)]}function Zw(e){const t=Fi(e);return[oa(e),t,oa(t)]}function oa(e){return e.replace(/start|end/g,t=>Yw[t])}const cd=["left","right"],dd=["right","left"],Jw=["top","bottom"],qw=["bottom","top"];function ex(e,t,n){switch(e){case"top":case"bottom":return n?t?dd:cd:t?cd:dd;case"left":case"right":return t?Jw:qw;default:return[]}}function tx(e,t,n,r){const o=dr(e);let i=ex(jt(e),n==="start",r);return o&&(i=i.map(l=>l+"-"+o),t&&(i=i.concat(i.map(oa)))),i}function Fi(e){return e.replace(/left|right|bottom|top/g,t=>Gw[t])}function nx(e){return{top:0,right:0,bottom:0,left:0,...e}}function pm(e){return typeof e!="number"?nx(e):{top:e,right:e,bottom:e,left:e}}function zi(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function fd(e,t,n){let{reference:r,floating:o}=e;const i=ft(t),l=hu(t),s=mu(l),a=jt(t),u=i==="y",f=r.x+r.width/2-o.width/2,c=r.y+r.height/2-o.height/2,m=r[s]/2-o[s]/2;let w;switch(a){case"top":w={x:f,y:r.y-o.height};break;case"bottom":w={x:f,y:r.y+r.height};break;case"right":w={x:r.x+r.width,y:c};break;case"left":w={x:r.x-o.width,y:c};break;default:w={x:r.x,y:r.y}}switch(dr(t)){case"start":w[l]-=m*(n&&u?-1:1);break;case"end":w[l]+=m*(n&&u?-1:1);break}return w}const rx=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,s=i.filter(Boolean),a=await(l.isRTL==null?void 0:l.isRTL(t));let u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:c}=fd(u,r,a),m=r,w={},x=0;for(let y=0;y<s.length;y++){const{name:S,fn:v}=s[y],{x:h,y:g,data:E,reset:k}=await v({x:f,y:c,initialPlacement:r,placement:m,strategy:o,middlewareData:w,rects:u,platform:l,elements:{reference:e,floating:t}});f=h??f,c=g??c,w={...w,[S]:{...w[S],...E}},k&&x<=50&&(x++,typeof k=="object"&&(k.placement&&(m=k.placement),k.rects&&(u=k.rects===!0?await l.getElementRects({reference:e,floating:t,strategy:o}):k.rects),{x:f,y:c}=fd(u,m,a)),y=-1)}return{x:f,y:c,placement:m,strategy:o,middlewareData:w}};async function ao(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:l,elements:s,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:c="floating",altBoundary:m=!1,padding:w=0}=At(t,e),x=pm(w),S=s[m?c==="floating"?"reference":"floating":c],v=zi(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(S)))==null||n?S:S.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:f,strategy:a})),h=c==="floating"?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,g=await(i.getOffsetParent==null?void 0:i.getOffsetParent(s.floating)),E=await(i.isElement==null?void 0:i.isElement(g))?await(i.getScale==null?void 0:i.getScale(g))||{x:1,y:1}:{x:1,y:1},k=zi(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:h,offsetParent:g,strategy:a}):h);return{top:(v.top-k.top+x.top)/E.y,bottom:(k.bottom-v.bottom+x.bottom)/E.y,left:(v.left-k.left+x.left)/E.x,right:(k.right-v.right+x.right)/E.x}}const ox=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:l,elements:s,middlewareData:a}=t,{element:u,padding:f=0}=At(e,t)||{};if(u==null)return{};const c=pm(f),m={x:n,y:r},w=hu(o),x=mu(w),y=await l.getDimensions(u),S=w==="y",v=S?"top":"left",h=S?"bottom":"right",g=S?"clientHeight":"clientWidth",E=i.reference[x]+i.reference[w]-m[w]-i.floating[x],k=m[w]-i.reference[w],P=await(l.getOffsetParent==null?void 0:l.getOffsetParent(u));let N=P?P[g]:0;(!N||!await(l.isElement==null?void 0:l.isElement(P)))&&(N=s.floating[g]||i.floating[x]);const T=E/2-k/2,M=N/2-y[x]/2-1,_=nn(c[v],M),L=nn(c[h],M),O=_,U=N-y[x]-L,j=N/2-y[x]/2+T,W=ra(O,j,U),F=!a.arrow&&dr(o)!=null&&j!==W&&i.reference[x]/2-(j<O?_:L)-y[x]/2<0,B=F?j<O?j-O:j-U:0;return{[w]:m[w]+B,data:{[w]:W,centerOffset:j-W-B,...F&&{alignmentOffset:B}},reset:F}}}),ix=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:l,initialPlacement:s,platform:a,elements:u}=t,{mainAxis:f=!0,crossAxis:c=!0,fallbackPlacements:m,fallbackStrategy:w="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:y=!0,...S}=At(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const v=jt(o),h=ft(s),g=jt(s)===s,E=await(a.isRTL==null?void 0:a.isRTL(u.floating)),k=m||(g||!y?[Fi(s)]:Zw(s)),P=x!=="none";!m&&P&&k.push(...tx(s,y,x,E));const N=[s,...k],T=await ao(t,S),M=[];let _=((r=i.flip)==null?void 0:r.overflows)||[];if(f&&M.push(T[v]),c){const j=Qw(o,l,E);M.push(T[j[0]],T[j[1]])}if(_=[..._,{placement:o,overflows:M}],!M.every(j=>j<=0)){var L,O;const j=(((L=i.flip)==null?void 0:L.index)||0)+1,W=N[j];if(W&&(!(c==="alignment"?h!==ft(W):!1)||_.every(C=>C.overflows[0]>0&&ft(C.placement)===h)))return{data:{index:j,overflows:_},reset:{placement:W}};let F=(O=_.filter(B=>B.overflows[0]<=0).sort((B,C)=>B.overflows[1]-C.overflows[1])[0])==null?void 0:O.placement;if(!F)switch(w){case"bestFit":{var U;const B=(U=_.filter(C=>{if(P){const R=ft(C.placement);return R===h||R==="y"}return!0}).map(C=>[C.placement,C.overflows.filter(R=>R>0).reduce((R,A)=>R+A,0)]).sort((C,R)=>C[1]-R[1])[0])==null?void 0:U[0];B&&(F=B);break}case"initialPlacement":F=s;break}if(o!==F)return{reset:{placement:F}}}return{}}}};function pd(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function md(e){return Kw.some(t=>e[t]>=0)}const lx=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=At(e,t);switch(r){case"referenceHidden":{const i=await ao(t,{...o,elementContext:"reference"}),l=pd(i,n.reference);return{data:{referenceHiddenOffsets:l,referenceHidden:md(l)}}}case"escaped":{const i=await ao(t,{...o,altBoundary:!0}),l=pd(i,n.floating);return{data:{escapedOffsets:l,escaped:md(l)}}}default:return{}}}}},mm=new Set(["left","top"]);async function sx(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),l=jt(n),s=dr(n),a=ft(n)==="y",u=mm.has(l)?-1:1,f=i&&a?-1:1,c=At(t,e);let{mainAxis:m,crossAxis:w,alignmentAxis:x}=typeof c=="number"?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return s&&typeof x=="number"&&(w=s==="end"?x*-1:x),a?{x:w*f,y:m*u}:{x:m*u,y:w*f}}const ax=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:l,middlewareData:s}=t,a=await sx(t,e);return l===((n=s.offset)==null?void 0:n.placement)&&(r=s.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:l}}}}},ux=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:s={fn:S=>{let{x:v,y:h}=S;return{x:v,y:h}}},...a}=At(e,t),u={x:n,y:r},f=await ao(t,a),c=ft(jt(o)),m=pu(c);let w=u[m],x=u[c];if(i){const S=m==="y"?"top":"left",v=m==="y"?"bottom":"right",h=w+f[S],g=w-f[v];w=ra(h,w,g)}if(l){const S=c==="y"?"top":"left",v=c==="y"?"bottom":"right",h=x+f[S],g=x-f[v];x=ra(h,x,g)}const y=s.fn({...t,[m]:w,[c]:x});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[m]:i,[c]:l}}}}}},cx=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:s=0,mainAxis:a=!0,crossAxis:u=!0}=At(e,t),f={x:n,y:r},c=ft(o),m=pu(c);let w=f[m],x=f[c];const y=At(s,t),S=typeof y=="number"?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(a){const g=m==="y"?"height":"width",E=i.reference[m]-i.floating[g]+S.mainAxis,k=i.reference[m]+i.reference[g]-S.mainAxis;w<E?w=E:w>k&&(w=k)}if(u){var v,h;const g=m==="y"?"width":"height",E=mm.has(jt(o)),k=i.reference[c]-i.floating[g]+(E&&((v=l.offset)==null?void 0:v[c])||0)+(E?0:S.crossAxis),P=i.reference[c]+i.reference[g]+(E?0:((h=l.offset)==null?void 0:h[c])||0)-(E?S.crossAxis:0);x<k?x=k:x>P&&(x=P)}return{[m]:w,[c]:x}}}},dx=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:l,elements:s}=t,{apply:a=()=>{},...u}=At(e,t),f=await ao(t,u),c=jt(o),m=dr(o),w=ft(o)==="y",{width:x,height:y}=i.floating;let S,v;c==="top"||c==="bottom"?(S=c,v=m===(await(l.isRTL==null?void 0:l.isRTL(s.floating))?"start":"end")?"left":"right"):(v=c,S=m==="end"?"top":"bottom");const h=y-f.top-f.bottom,g=x-f.left-f.right,E=nn(y-f[S],h),k=nn(x-f[v],g),P=!t.middlewareData.shift;let N=E,T=k;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(T=g),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(N=h),P&&!m){const _=Le(f.left,0),L=Le(f.right,0),O=Le(f.top,0),U=Le(f.bottom,0);w?T=x-2*(_!==0||L!==0?_+L:Le(f.left,f.right)):N=y-2*(O!==0||U!==0?O+U:Le(f.top,f.bottom))}await a({...t,availableWidth:T,availableHeight:N});const M=await l.getDimensions(s.floating);return x!==M.width||y!==M.height?{reset:{rects:!0}}:{}}}};function al(){return typeof window<"u"}function fr(e){return hm(e)?(e.nodeName||"").toLowerCase():"#document"}function ze(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function yt(e){var t;return(t=(hm(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function hm(e){return al()?e instanceof Node||e instanceof ze(e).Node:!1}function it(e){return al()?e instanceof Element||e instanceof ze(e).Element:!1}function gt(e){return al()?e instanceof HTMLElement||e instanceof ze(e).HTMLElement:!1}function hd(e){return!al()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof ze(e).ShadowRoot}const fx=new Set(["inline","contents"]);function wo(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=lt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!fx.has(o)}const px=new Set(["table","td","th"]);function mx(e){return px.has(fr(e))}const hx=[":popover-open",":modal"];function ul(e){return hx.some(t=>{try{return e.matches(t)}catch{return!1}})}const vx=["transform","translate","scale","rotate","perspective"],gx=["transform","translate","scale","rotate","perspective","filter"],yx=["paint","layout","strict","content"];function vu(e){const t=gu(),n=it(e)?lt(e):e;return vx.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||gx.some(r=>(n.willChange||"").includes(r))||yx.some(r=>(n.contain||"").includes(r))}function wx(e){let t=rn(e);for(;gt(t)&&!ir(t);){if(vu(t))return t;if(ul(t))return null;t=rn(t)}return null}function gu(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const xx=new Set(["html","body","#document"]);function ir(e){return xx.has(fr(e))}function lt(e){return ze(e).getComputedStyle(e)}function cl(e){return it(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function rn(e){if(fr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||hd(e)&&e.host||yt(e);return hd(t)?t.host:t}function vm(e){const t=rn(e);return ir(t)?e.ownerDocument?e.ownerDocument.body:e.body:gt(t)&&wo(t)?t:vm(t)}function uo(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=vm(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),l=ze(o);if(i){const s=ia(l);return t.concat(l,l.visualViewport||[],wo(o)?o:[],s&&n?uo(s):[])}return t.concat(o,uo(o,[],n))}function ia(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function gm(e){const t=lt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=gt(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,s=Di(n)!==i||Di(r)!==l;return s&&(n=i,r=l),{width:n,height:r,$:s}}function yu(e){return it(e)?e:e.contextElement}function Qn(e){const t=yu(e);if(!gt(t))return ht(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=gm(t);let l=(i?Di(n.width):n.width)/r,s=(i?Di(n.height):n.height)/o;return(!l||!Number.isFinite(l))&&(l=1),(!s||!Number.isFinite(s))&&(s=1),{x:l,y:s}}const Sx=ht(0);function ym(e){const t=ze(e);return!gu()||!t.visualViewport?Sx:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Ex(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==ze(e)?!1:t}function Sn(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=yu(e);let l=ht(1);t&&(r?it(r)&&(l=Qn(r)):l=Qn(e));const s=Ex(i,n,r)?ym(i):ht(0);let a=(o.left+s.x)/l.x,u=(o.top+s.y)/l.y,f=o.width/l.x,c=o.height/l.y;if(i){const m=ze(i),w=r&&it(r)?ze(r):r;let x=m,y=ia(x);for(;y&&r&&w!==x;){const S=Qn(y),v=y.getBoundingClientRect(),h=lt(y),g=v.left+(y.clientLeft+parseFloat(h.paddingLeft))*S.x,E=v.top+(y.clientTop+parseFloat(h.paddingTop))*S.y;a*=S.x,u*=S.y,f*=S.x,c*=S.y,a+=g,u+=E,x=ze(y),y=ia(x)}}return zi({width:f,height:c,x:a,y:u})}function wu(e,t){const n=cl(e).scrollLeft;return t?t.left+n:Sn(yt(e)).left+n}function wm(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:wu(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function Cx(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",l=yt(r),s=t?ul(t.floating):!1;if(r===l||s&&i)return n;let a={scrollLeft:0,scrollTop:0},u=ht(1);const f=ht(0),c=gt(r);if((c||!c&&!i)&&((fr(r)!=="body"||wo(l))&&(a=cl(r)),gt(r))){const w=Sn(r);u=Qn(r),f.x=w.x+r.clientLeft,f.y=w.y+r.clientTop}const m=l&&!c&&!i?wm(l,a,!0):ht(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+f.x+m.x,y:n.y*u.y-a.scrollTop*u.y+f.y+m.y}}function kx(e){return Array.from(e.getClientRects())}function Nx(e){const t=yt(e),n=cl(e),r=e.ownerDocument.body,o=Le(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Le(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let l=-n.scrollLeft+wu(e);const s=-n.scrollTop;return lt(r).direction==="rtl"&&(l+=Le(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:s}}function Px(e,t){const n=ze(e),r=yt(e),o=n.visualViewport;let i=r.clientWidth,l=r.clientHeight,s=0,a=0;if(o){i=o.width,l=o.height;const u=gu();(!u||u&&t==="fixed")&&(s=o.offsetLeft,a=o.offsetTop)}return{width:i,height:l,x:s,y:a}}const Rx=new Set(["absolute","fixed"]);function Tx(e,t){const n=Sn(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=gt(e)?Qn(e):ht(1),l=e.clientWidth*i.x,s=e.clientHeight*i.y,a=o*i.x,u=r*i.y;return{width:l,height:s,x:a,y:u}}function vd(e,t,n){let r;if(t==="viewport")r=Px(e,n);else if(t==="document")r=Nx(yt(e));else if(it(t))r=Tx(t,n);else{const o=ym(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return zi(r)}function xm(e,t){const n=rn(e);return n===t||!it(n)||ir(n)?!1:lt(n).position==="fixed"||xm(n,t)}function bx(e,t){const n=t.get(e);if(n)return n;let r=uo(e,[],!1).filter(s=>it(s)&&fr(s)!=="body"),o=null;const i=lt(e).position==="fixed";let l=i?rn(e):e;for(;it(l)&&!ir(l);){const s=lt(l),a=vu(l);!a&&s.position==="fixed"&&(o=null),(i?!a&&!o:!a&&s.position==="static"&&!!o&&Rx.has(o.position)||wo(l)&&!a&&xm(e,l))?r=r.filter(f=>f!==l):o=s,l=rn(l)}return t.set(e,r),r}function _x(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const l=[...n==="clippingAncestors"?ul(t)?[]:bx(t,this._c):[].concat(n),r],s=l[0],a=l.reduce((u,f)=>{const c=vd(t,f,o);return u.top=Le(c.top,u.top),u.right=nn(c.right,u.right),u.bottom=nn(c.bottom,u.bottom),u.left=Le(c.left,u.left),u},vd(t,s,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function Mx(e){const{width:t,height:n}=gm(e);return{width:t,height:n}}function Ax(e,t,n){const r=gt(t),o=yt(t),i=n==="fixed",l=Sn(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const a=ht(0);function u(){a.x=wu(o)}if(r||!r&&!i)if((fr(t)!=="body"||wo(o))&&(s=cl(t)),r){const w=Sn(t,!0,i,t);a.x=w.x+t.clientLeft,a.y=w.y+t.clientTop}else o&&u();i&&!r&&o&&u();const f=o&&!r&&!i?wm(o,s):ht(0),c=l.left+s.scrollLeft-a.x-f.x,m=l.top+s.scrollTop-a.y-f.y;return{x:c,y:m,width:l.width,height:l.height}}function Xl(e){return lt(e).position==="static"}function gd(e,t){if(!gt(e)||lt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return yt(e)===n&&(n=n.ownerDocument.body),n}function Sm(e,t){const n=ze(e);if(ul(e))return n;if(!gt(e)){let o=rn(e);for(;o&&!ir(o);){if(it(o)&&!Xl(o))return o;o=rn(o)}return n}let r=gd(e,t);for(;r&&mx(r)&&Xl(r);)r=gd(r,t);return r&&ir(r)&&Xl(r)&&!vu(r)?n:r||wx(e)||n}const jx=async function(e){const t=this.getOffsetParent||Sm,n=this.getDimensions,r=await n(e.floating);return{reference:Ax(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Ix(e){return lt(e).direction==="rtl"}const Ox={convertOffsetParentRelativeRectToViewportRelativeRect:Cx,getDocumentElement:yt,getClippingRect:_x,getOffsetParent:Sm,getElementRects:jx,getClientRects:kx,getDimensions:Mx,getScale:Qn,isElement:it,isRTL:Ix};function Em(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Lx(e,t){let n=null,r;const o=yt(e);function i(){var s;clearTimeout(r),(s=n)==null||s.disconnect(),n=null}function l(s,a){s===void 0&&(s=!1),a===void 0&&(a=1),i();const u=e.getBoundingClientRect(),{left:f,top:c,width:m,height:w}=u;if(s||t(),!m||!w)return;const x=Wo(c),y=Wo(o.clientWidth-(f+m)),S=Wo(o.clientHeight-(c+w)),v=Wo(f),g={rootMargin:-x+"px "+-y+"px "+-S+"px "+-v+"px",threshold:Le(0,nn(1,a))||1};let E=!0;function k(P){const N=P[0].intersectionRatio;if(N!==a){if(!E)return l();N?l(!1,N):r=setTimeout(()=>{l(!1,1e-7)},1e3)}N===1&&!Em(u,e.getBoundingClientRect())&&l(),E=!1}try{n=new IntersectionObserver(k,{...g,root:o.ownerDocument})}catch{n=new IntersectionObserver(k,g)}n.observe(e)}return l(!0),i}function Dx(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:l=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=yu(e),f=o||i?[...u?uo(u):[],...uo(t)]:[];f.forEach(v=>{o&&v.addEventListener("scroll",n,{passive:!0}),i&&v.addEventListener("resize",n)});const c=u&&s?Lx(u,n):null;let m=-1,w=null;l&&(w=new ResizeObserver(v=>{let[h]=v;h&&h.target===u&&w&&(w.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var g;(g=w)==null||g.observe(t)})),n()}),u&&!a&&w.observe(u),w.observe(t));let x,y=a?Sn(e):null;a&&S();function S(){const v=Sn(e);y&&!Em(y,v)&&n(),y=v,x=requestAnimationFrame(S)}return n(),()=>{var v;f.forEach(h=>{o&&h.removeEventListener("scroll",n),i&&h.removeEventListener("resize",n)}),c==null||c(),(v=w)==null||v.disconnect(),w=null,a&&cancelAnimationFrame(x)}}const Fx=ax,zx=ux,$x=ix,Ux=dx,Wx=lx,yd=ox,Vx=cx,Bx=(e,t,n)=>{const r=new Map,o={platform:Ox,...n},i={...o.platform,_c:r};return rx(e,t,{...o,platform:i})};var Hx=typeof document<"u",Kx=function(){},si=Hx?p.useLayoutEffect:Kx;function $i(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!$i(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!$i(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Cm(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function wd(e,t){const n=Cm(e);return Math.round(t*n)/n}function Ql(e){const t=p.useRef(e);return si(()=>{t.current=e}),t}function Gx(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:s=!0,whileElementsMounted:a,open:u}=e,[f,c]=p.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,w]=p.useState(r);$i(m,r)||w(r);const[x,y]=p.useState(null),[S,v]=p.useState(null),h=p.useCallback(C=>{C!==P.current&&(P.current=C,y(C))},[]),g=p.useCallback(C=>{C!==N.current&&(N.current=C,v(C))},[]),E=i||x,k=l||S,P=p.useRef(null),N=p.useRef(null),T=p.useRef(f),M=a!=null,_=Ql(a),L=Ql(o),O=Ql(u),U=p.useCallback(()=>{if(!P.current||!N.current)return;const C={placement:t,strategy:n,middleware:m};L.current&&(C.platform=L.current),Bx(P.current,N.current,C).then(R=>{const A={...R,isPositioned:O.current!==!1};j.current&&!$i(T.current,A)&&(T.current=A,yo.flushSync(()=>{c(A)}))})},[m,t,n,L,O]);si(()=>{u===!1&&T.current.isPositioned&&(T.current.isPositioned=!1,c(C=>({...C,isPositioned:!1})))},[u]);const j=p.useRef(!1);si(()=>(j.current=!0,()=>{j.current=!1}),[]),si(()=>{if(E&&(P.current=E),k&&(N.current=k),E&&k){if(_.current)return _.current(E,k,U);U()}},[E,k,U,_,M]);const W=p.useMemo(()=>({reference:P,floating:N,setReference:h,setFloating:g}),[h,g]),F=p.useMemo(()=>({reference:E,floating:k}),[E,k]),B=p.useMemo(()=>{const C={position:n,left:0,top:0};if(!F.floating)return C;const R=wd(F.floating,f.x),A=wd(F.floating,f.y);return s?{...C,transform:"translate("+R+"px, "+A+"px)",...Cm(F.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:R,top:A}},[n,s,F.floating,f.x,f.y]);return p.useMemo(()=>({...f,update:U,refs:W,elements:F,floatingStyles:B}),[f,U,W,F,B])}const Yx=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?yd({element:r.current,padding:o}).fn(n):{}:r?yd({element:r,padding:o}).fn(n):{}}}},Xx=(e,t)=>({...Fx(e),options:[e,t]}),Qx=(e,t)=>({...zx(e),options:[e,t]}),Zx=(e,t)=>({...Vx(e),options:[e,t]}),Jx=(e,t)=>({...$x(e),options:[e,t]}),qx=(e,t)=>({...Ux(e),options:[e,t]}),e1=(e,t)=>({...Wx(e),options:[e,t]}),t1=(e,t)=>({...Yx(e),options:[e,t]});var n1="Arrow",km=p.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return d.jsx(Y.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:d.jsx("polygon",{points:"0,0 30,0 15,10"})})});km.displayName=n1;var r1=km;function o1(e){const[t,n]=p.useState(void 0);return vt(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let l,s;if("borderBoxSize"in i){const a=i.borderBoxSize,u=Array.isArray(a)?a[0]:a;l=u.inlineSize,s=u.blockSize}else l=e.offsetWidth,s=e.offsetHeight;n({width:l,height:s})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var xu="Popper",[Nm,Pm]=kn(xu),[i1,Rm]=Nm(xu),Tm=e=>{const{__scopePopper:t,children:n}=e,[r,o]=p.useState(null);return d.jsx(i1,{scope:t,anchor:r,onAnchorChange:o,children:n})};Tm.displayName=xu;var bm="PopperAnchor",_m=p.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Rm(bm,n),l=p.useRef(null),s=le(t,l);return p.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||l.current)}),r?null:d.jsx(Y.div,{...o,ref:s})});_m.displayName=bm;var Su="PopperContent",[l1,s1]=Nm(Su),Mm=p.forwardRef((e,t)=>{var be,pr,Ve,mr,Fu,zu;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:l=0,arrowPadding:s=0,avoidCollisions:a=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:c="partial",hideWhenDetached:m=!1,updatePositionStrategy:w="optimized",onPlaced:x,...y}=e,S=Rm(Su,n),[v,h]=p.useState(null),g=le(t,hr=>h(hr)),[E,k]=p.useState(null),P=o1(E),N=(P==null?void 0:P.width)??0,T=(P==null?void 0:P.height)??0,M=r+(i!=="center"?"-"+i:""),_=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},L=Array.isArray(u)?u:[u],O=L.length>0,U={padding:_,boundary:L.filter(u1),altBoundary:O},{refs:j,floatingStyles:W,placement:F,isPositioned:B,middlewareData:C}=Gx({strategy:"fixed",placement:M,whileElementsMounted:(...hr)=>Dx(...hr,{animationFrame:w==="always"}),elements:{reference:S.anchor},middleware:[Xx({mainAxis:o+T,alignmentAxis:l}),a&&Qx({mainAxis:!0,crossAxis:!1,limiter:c==="partial"?Zx():void 0,...U}),a&&Jx({...U}),qx({...U,apply:({elements:hr,rects:$u,availableWidth:Ov,availableHeight:Lv})=>{const{width:Dv,height:Fv}=$u.reference,ko=hr.floating.style;ko.setProperty("--radix-popper-available-width",`${Ov}px`),ko.setProperty("--radix-popper-available-height",`${Lv}px`),ko.setProperty("--radix-popper-anchor-width",`${Dv}px`),ko.setProperty("--radix-popper-anchor-height",`${Fv}px`)}}),E&&t1({element:E,padding:s}),c1({arrowWidth:N,arrowHeight:T}),m&&e1({strategy:"referenceHidden",...U})]}),[R,A]=Im(F),z=ue(x);vt(()=>{B&&(z==null||z())},[B,z]);const H=(be=C.arrow)==null?void 0:be.x,Ee=(pr=C.arrow)==null?void 0:pr.y,ge=((Ve=C.arrow)==null?void 0:Ve.centerOffset)!==0,[Ot,Ce]=p.useState();return vt(()=>{v&&Ce(window.getComputedStyle(v).zIndex)},[v]),d.jsx("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:B?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ot,"--radix-popper-transform-origin":[(mr=C.transformOrigin)==null?void 0:mr.x,(Fu=C.transformOrigin)==null?void 0:Fu.y].join(" "),...((zu=C.hide)==null?void 0:zu.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:d.jsx(l1,{scope:n,placedSide:R,onArrowChange:k,arrowX:H,arrowY:Ee,shouldHideArrow:ge,children:d.jsx(Y.div,{"data-side":R,"data-align":A,...y,ref:g,style:{...y.style,animation:B?void 0:"none"}})})})});Mm.displayName=Su;var Am="PopperArrow",a1={top:"bottom",right:"left",bottom:"top",left:"right"},jm=p.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=s1(Am,r),l=a1[i.placedSide];return d.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:d.jsx(r1,{...o,ref:n,style:{...o.style,display:"block"}})})});jm.displayName=Am;function u1(e){return e!==null}var c1=e=>({name:"transformOrigin",options:e,fn(t){var S,v,h;const{placement:n,rects:r,middlewareData:o}=t,l=((S=o.arrow)==null?void 0:S.centerOffset)!==0,s=l?0:e.arrowWidth,a=l?0:e.arrowHeight,[u,f]=Im(n),c={start:"0%",center:"50%",end:"100%"}[f],m=(((v=o.arrow)==null?void 0:v.x)??0)+s/2,w=(((h=o.arrow)==null?void 0:h.y)??0)+a/2;let x="",y="";return u==="bottom"?(x=l?c:`${m}px`,y=`${-a}px`):u==="top"?(x=l?c:`${m}px`,y=`${r.floating.height+a}px`):u==="right"?(x=`${-a}px`,y=l?c:`${w}px`):u==="left"&&(x=`${r.floating.width+a}px`,y=l?c:`${w}px`),{data:{x,y}}}});function Im(e){const[t,n="center"]=e.split("-");return[t,n]}var d1=Tm,f1=_m,p1=Mm,m1=jm,h1="Portal",Eu=p.forwardRef((e,t)=>{var s;const{container:n,...r}=e,[o,i]=p.useState(!1);vt(()=>i(!0),[]);const l=n||o&&((s=globalThis==null?void 0:globalThis.document)==null?void 0:s.body);return l?d0.createPortal(d.jsx(Y.div,{...r,ref:t}),l):null});Eu.displayName=h1;function v1(e,t){return p.useReducer((n,r)=>t[n][r]??n,e)}var wt=e=>{const{present:t,children:n}=e,r=g1(t),o=typeof n=="function"?n({present:r.isPresent}):p.Children.only(n),i=le(r.ref,y1(o));return typeof n=="function"||r.isPresent?p.cloneElement(o,{ref:i}):null};wt.displayName="Presence";function g1(e){const[t,n]=p.useState(),r=p.useRef(null),o=p.useRef(e),i=p.useRef("none"),l=e?"mounted":"unmounted",[s,a]=v1(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return p.useEffect(()=>{const u=Vo(r.current);i.current=s==="mounted"?u:"none"},[s]),vt(()=>{const u=r.current,f=o.current;if(f!==e){const m=i.current,w=Vo(u);e?a("MOUNT"):w==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(f&&m!==w?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),vt(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,c=w=>{const y=Vo(r.current).includes(w.animationName);if(w.target===t&&y&&(a("ANIMATION_END"),!o.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},m=w=>{w.target===t&&(i.current=Vo(r.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",c),t.addEventListener("animationend",c),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",c),t.removeEventListener("animationend",c)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:p.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function Vo(e){return(e==null?void 0:e.animationName)||"none"}function y1(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Zl="rovingFocusGroup.onEntryFocus",w1={bubbles:!1,cancelable:!0},xo="RovingFocusGroup",[la,Om,x1]=cu(xo),[S1,Lm]=kn(xo,[x1]),[E1,C1]=S1(xo),Dm=p.forwardRef((e,t)=>d.jsx(la.Provider,{scope:e.__scopeRovingFocusGroup,children:d.jsx(la.Slot,{scope:e.__scopeRovingFocusGroup,children:d.jsx(k1,{...e,ref:t})})}));Dm.displayName=xo;var k1=p.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:l,defaultCurrentTabStopId:s,onCurrentTabStopIdChange:a,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...c}=e,m=p.useRef(null),w=le(t,m),x=du(i),[y,S]=au({prop:l,defaultProp:s??null,onChange:a,caller:xo}),[v,h]=p.useState(!1),g=ue(u),E=Om(n),k=p.useRef(!1),[P,N]=p.useState(0);return p.useEffect(()=>{const T=m.current;if(T)return T.addEventListener(Zl,g),()=>T.removeEventListener(Zl,g)},[g]),d.jsx(E1,{scope:n,orientation:r,dir:x,loop:o,currentTabStopId:y,onItemFocus:p.useCallback(T=>S(T),[S]),onItemShiftTab:p.useCallback(()=>h(!0),[]),onFocusableItemAdd:p.useCallback(()=>N(T=>T+1),[]),onFocusableItemRemove:p.useCallback(()=>N(T=>T-1),[]),children:d.jsx(Y.div,{tabIndex:v||P===0?-1:0,"data-orientation":r,...c,ref:w,style:{outline:"none",...e.style},onMouseDown:D(e.onMouseDown,()=>{k.current=!0}),onFocus:D(e.onFocus,T=>{const M=!k.current;if(T.target===T.currentTarget&&M&&!v){const _=new CustomEvent(Zl,w1);if(T.currentTarget.dispatchEvent(_),!_.defaultPrevented){const L=E().filter(F=>F.focusable),O=L.find(F=>F.active),U=L.find(F=>F.id===y),W=[O,U,...L].filter(Boolean).map(F=>F.ref.current);$m(W,f)}}k.current=!1}),onBlur:D(e.onBlur,()=>h(!1))})})}),Fm="RovingFocusGroupItem",zm=p.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,children:l,...s}=e,a=na(),u=i||a,f=C1(Fm,n),c=f.currentTabStopId===u,m=Om(n),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:y}=f;return p.useEffect(()=>{if(r)return w(),()=>x()},[r,w,x]),d.jsx(la.ItemSlot,{scope:n,id:u,focusable:r,active:o,children:d.jsx(Y.span,{tabIndex:c?0:-1,"data-orientation":f.orientation,...s,ref:t,onMouseDown:D(e.onMouseDown,S=>{r?f.onItemFocus(u):S.preventDefault()}),onFocus:D(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:D(e.onKeyDown,S=>{if(S.key==="Tab"&&S.shiftKey){f.onItemShiftTab();return}if(S.target!==S.currentTarget)return;const v=R1(S,f.orientation,f.dir);if(v!==void 0){if(S.metaKey||S.ctrlKey||S.altKey||S.shiftKey)return;S.preventDefault();let g=m().filter(E=>E.focusable).map(E=>E.ref.current);if(v==="last")g.reverse();else if(v==="prev"||v==="next"){v==="prev"&&g.reverse();const E=g.indexOf(S.currentTarget);g=f.loop?T1(g,E+1):g.slice(E+1)}setTimeout(()=>$m(g))}}),children:typeof l=="function"?l({isCurrentTabStop:c,hasTabStop:y!=null}):l})})});zm.displayName=Fm;var N1={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P1(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function R1(e,t,n){const r=P1(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return N1[r]}function $m(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function T1(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var b1=Dm,_1=zm,M1=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Tn=new WeakMap,Bo=new WeakMap,Ho={},Jl=0,Um=function(e){return e&&(e.host||Um(e.parentNode))},A1=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Um(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},j1=function(e,t,n,r){var o=A1(t,Array.isArray(e)?e:[e]);Ho[n]||(Ho[n]=new WeakMap);var i=Ho[n],l=[],s=new Set,a=new Set(o),u=function(c){!c||s.has(c)||(s.add(c),u(c.parentNode))};o.forEach(u);var f=function(c){!c||a.has(c)||Array.prototype.forEach.call(c.children,function(m){if(s.has(m))f(m);else try{var w=m.getAttribute(r),x=w!==null&&w!=="false",y=(Tn.get(m)||0)+1,S=(i.get(m)||0)+1;Tn.set(m,y),i.set(m,S),l.push(m),y===1&&x&&Bo.set(m,!0),S===1&&m.setAttribute(n,"true"),x||m.setAttribute(r,"true")}catch(v){console.error("aria-hidden: cannot operate on ",m,v)}})};return f(t),s.clear(),Jl++,function(){l.forEach(function(c){var m=Tn.get(c)-1,w=i.get(c)-1;Tn.set(c,m),i.set(c,w),m||(Bo.has(c)||c.removeAttribute(r),Bo.delete(c)),w||c.removeAttribute(n)}),Jl--,Jl||(Tn=new WeakMap,Tn=new WeakMap,Bo=new WeakMap,Ho={})}},I1=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||M1(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),j1(r,o,n,"aria-hidden")):function(){return null}},dt=function(){return dt=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},dt.apply(this,arguments)};function Wm(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function O1(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var ai="right-scroll-bar-position",ui="width-before-scroll-bar",L1="with-scroll-bars-hidden",D1="--removed-body-scroll-bar-size";function ql(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function F1(e,t){var n=p.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var z1=typeof window<"u"?p.useLayoutEffect:p.useEffect,xd=new WeakMap;function $1(e,t){var n=F1(t||null,function(r){return e.forEach(function(o){return ql(o,r)})});return z1(function(){var r=xd.get(n);if(r){var o=new Set(r),i=new Set(e),l=n.current;o.forEach(function(s){i.has(s)||ql(s,null)}),i.forEach(function(s){o.has(s)||ql(s,l)})}xd.set(n,e)},[e]),n}function U1(e){return e}function W1(e,t){t===void 0&&(t=U1);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var l=t(i,r);return n.push(l),function(){n=n.filter(function(s){return s!==l})}},assignSyncMedium:function(i){for(r=!0;n.length;){var l=n;n=[],l.forEach(i)}n={push:function(s){return i(s)},filter:function(){return n}}},assignMedium:function(i){r=!0;var l=[];if(n.length){var s=n;n=[],s.forEach(i),l=n}var a=function(){var f=l;l=[],f.forEach(i)},u=function(){return Promise.resolve().then(a)};u(),n={push:function(f){l.push(f),u()},filter:function(f){return l=l.filter(f),n}}}};return o}function V1(e){e===void 0&&(e={});var t=W1(null);return t.options=dt({async:!0,ssr:!1},e),t}var Vm=function(e){var t=e.sideCar,n=Wm(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return p.createElement(r,dt({},n))};Vm.isSideCarExport=!0;function B1(e,t){return e.useMedium(t),Vm}var Bm=V1(),es=function(){},dl=p.forwardRef(function(e,t){var n=p.useRef(null),r=p.useState({onScrollCapture:es,onWheelCapture:es,onTouchMoveCapture:es}),o=r[0],i=r[1],l=e.forwardProps,s=e.children,a=e.className,u=e.removeScrollBar,f=e.enabled,c=e.shards,m=e.sideCar,w=e.noRelative,x=e.noIsolation,y=e.inert,S=e.allowPinchZoom,v=e.as,h=v===void 0?"div":v,g=e.gapMode,E=Wm(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=m,P=$1([n,t]),N=dt(dt({},E),o);return p.createElement(p.Fragment,null,f&&p.createElement(k,{sideCar:Bm,removeScrollBar:u,shards:c,noRelative:w,noIsolation:x,inert:y,setCallbacks:i,allowPinchZoom:!!S,lockRef:n,gapMode:g}),l?p.cloneElement(p.Children.only(s),dt(dt({},N),{ref:P})):p.createElement(h,dt({},N,{className:a,ref:P}),s))});dl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};dl.classNames={fullWidth:ui,zeroRight:ai};var Sd,H1=function(){if(Sd)return Sd;if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function K1(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=H1();return t&&e.setAttribute("nonce",t),e}function G1(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Y1(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var X1=function(){var e=0,t=null;return{add:function(n){e==0&&(t=K1())&&(G1(t,n),Y1(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Q1=function(){var e=X1();return function(t,n){p.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Hm=function(){var e=Q1(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Z1={left:0,top:0,right:0,gap:0},ts=function(e){return parseInt(e||"",10)||0},J1=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[ts(n),ts(r),ts(o)]},q1=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Z1;var t=J1(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},eS=Hm(),Zn="data-scroll-locked",tS=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,s=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(L1,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(s,"px ").concat(r,`;
  }
  body[`).concat(Zn,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(l,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(s,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(ai,` {
    right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat(ui,` {
    margin-right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat(ai," .").concat(ai,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ui," .").concat(ui,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Zn,`] {
    `).concat(D1,": ").concat(s,`px;
  }
`)},Ed=function(){var e=parseInt(document.body.getAttribute(Zn)||"0",10);return isFinite(e)?e:0},nS=function(){p.useEffect(function(){return document.body.setAttribute(Zn,(Ed()+1).toString()),function(){var e=Ed()-1;e<=0?document.body.removeAttribute(Zn):document.body.setAttribute(Zn,e.toString())}},[])},rS=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;nS();var i=p.useMemo(function(){return q1(o)},[o]);return p.createElement(eS,{styles:tS(i,!t,o,n?"":"!important")})},sa=!1;if(typeof window<"u")try{var Ko=Object.defineProperty({},"passive",{get:function(){return sa=!0,!0}});window.addEventListener("test",Ko,Ko),window.removeEventListener("test",Ko,Ko)}catch{sa=!1}var bn=sa?{passive:!1}:!1,oS=function(e){return e.tagName==="TEXTAREA"},Km=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!oS(e)&&n[t]==="visible")},iS=function(e){return Km(e,"overflowY")},lS=function(e){return Km(e,"overflowX")},Cd=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Gm(e,r);if(o){var i=Ym(e,r),l=i[1],s=i[2];if(l>s)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},sS=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},aS=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Gm=function(e,t){return e==="v"?iS(t):lS(t)},Ym=function(e,t){return e==="v"?sS(t):aS(t)},uS=function(e,t){return e==="h"&&t==="rtl"?-1:1},cS=function(e,t,n,r,o){var i=uS(e,window.getComputedStyle(t).direction),l=i*r,s=n.target,a=t.contains(s),u=!1,f=l>0,c=0,m=0;do{if(!s)break;var w=Ym(e,s),x=w[0],y=w[1],S=w[2],v=y-S-i*x;(x||v)&&Gm(e,s)&&(c+=v,m+=x);var h=s.parentNode;s=h&&h.nodeType===Node.DOCUMENT_FRAGMENT_NODE?h.host:h}while(!a&&s!==document.body||a&&(t.contains(s)||t===s));return(f&&(o&&Math.abs(c)<1||!o&&l>c)||!f&&(o&&Math.abs(m)<1||!o&&-l>m))&&(u=!0),u},Go=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},kd=function(e){return[e.deltaX,e.deltaY]},Nd=function(e){return e&&"current"in e?e.current:e},dS=function(e,t){return e[0]===t[0]&&e[1]===t[1]},fS=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},pS=0,_n=[];function mS(e){var t=p.useRef([]),n=p.useRef([0,0]),r=p.useRef(),o=p.useState(pS++)[0],i=p.useState(Hm)[0],l=p.useRef(e);p.useEffect(function(){l.current=e},[e]),p.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var y=O1([e.lockRef.current],(e.shards||[]).map(Nd),!0).filter(Boolean);return y.forEach(function(S){return S.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),y.forEach(function(S){return S.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=p.useCallback(function(y,S){if("touches"in y&&y.touches.length===2||y.type==="wheel"&&y.ctrlKey)return!l.current.allowPinchZoom;var v=Go(y),h=n.current,g="deltaX"in y?y.deltaX:h[0]-v[0],E="deltaY"in y?y.deltaY:h[1]-v[1],k,P=y.target,N=Math.abs(g)>Math.abs(E)?"h":"v";if("touches"in y&&N==="h"&&P.type==="range")return!1;var T=Cd(N,P);if(!T)return!0;if(T?k=N:(k=N==="v"?"h":"v",T=Cd(N,P)),!T)return!1;if(!r.current&&"changedTouches"in y&&(g||E)&&(r.current=k),!k)return!0;var M=r.current||k;return cS(M,S,y,M==="h"?g:E,!0)},[]),a=p.useCallback(function(y){var S=y;if(!(!_n.length||_n[_n.length-1]!==i)){var v="deltaY"in S?kd(S):Go(S),h=t.current.filter(function(k){return k.name===S.type&&(k.target===S.target||S.target===k.shadowParent)&&dS(k.delta,v)})[0];if(h&&h.should){S.cancelable&&S.preventDefault();return}if(!h){var g=(l.current.shards||[]).map(Nd).filter(Boolean).filter(function(k){return k.contains(S.target)}),E=g.length>0?s(S,g[0]):!l.current.noIsolation;E&&S.cancelable&&S.preventDefault()}}},[]),u=p.useCallback(function(y,S,v,h){var g={name:y,delta:S,target:v,should:h,shadowParent:hS(v)};t.current.push(g),setTimeout(function(){t.current=t.current.filter(function(E){return E!==g})},1)},[]),f=p.useCallback(function(y){n.current=Go(y),r.current=void 0},[]),c=p.useCallback(function(y){u(y.type,kd(y),y.target,s(y,e.lockRef.current))},[]),m=p.useCallback(function(y){u(y.type,Go(y),y.target,s(y,e.lockRef.current))},[]);p.useEffect(function(){return _n.push(i),e.setCallbacks({onScrollCapture:c,onWheelCapture:c,onTouchMoveCapture:m}),document.addEventListener("wheel",a,bn),document.addEventListener("touchmove",a,bn),document.addEventListener("touchstart",f,bn),function(){_n=_n.filter(function(y){return y!==i}),document.removeEventListener("wheel",a,bn),document.removeEventListener("touchmove",a,bn),document.removeEventListener("touchstart",f,bn)}},[]);var w=e.removeScrollBar,x=e.inert;return p.createElement(p.Fragment,null,x?p.createElement(i,{styles:fS(o)}):null,w?p.createElement(rS,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function hS(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const vS=B1(Bm,mS);var Xm=p.forwardRef(function(e,t){return p.createElement(dl,dt({},e,{ref:t,sideCar:vS}))});Xm.classNames=dl.classNames;const gS=Xm;var aa=["Enter"," "],yS=["ArrowDown","PageUp","Home"],Qm=["ArrowUp","PageDown","End"],wS=[...yS,...Qm],xS={ltr:[...aa,"ArrowRight"],rtl:[...aa,"ArrowLeft"]},SS={ltr:["ArrowLeft"],rtl:["ArrowRight"]},So="Menu",[co,ES,CS]=cu(So),[Nn,Zm]=kn(So,[CS,Pm,Lm]),fl=Pm(),Jm=Lm(),[kS,Pn]=Nn(So),[NS,Eo]=Nn(So),qm=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:i,modal:l=!0}=e,s=fl(t),[a,u]=p.useState(null),f=p.useRef(!1),c=ue(i),m=du(o);return p.useEffect(()=>{const w=()=>{f.current=!0,document.addEventListener("pointerdown",x,{capture:!0,once:!0}),document.addEventListener("pointermove",x,{capture:!0,once:!0})},x=()=>f.current=!1;return document.addEventListener("keydown",w,{capture:!0}),()=>{document.removeEventListener("keydown",w,{capture:!0}),document.removeEventListener("pointerdown",x,{capture:!0}),document.removeEventListener("pointermove",x,{capture:!0})}},[]),d.jsx(d1,{...s,children:d.jsx(kS,{scope:t,open:n,onOpenChange:c,content:a,onContentChange:u,children:d.jsx(NS,{scope:t,onClose:p.useCallback(()=>c(!1),[c]),isUsingKeyboardRef:f,dir:m,modal:l,children:r})})})};qm.displayName=So;var PS="MenuAnchor",Cu=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=fl(n);return d.jsx(f1,{...o,...r,ref:t})});Cu.displayName=PS;var ku="MenuPortal",[RS,eh]=Nn(ku,{forceMount:void 0}),th=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=Pn(ku,t);return d.jsx(RS,{scope:t,forceMount:n,children:d.jsx(wt,{present:n||i.open,children:d.jsx(Eu,{asChild:!0,container:o,children:r})})})};th.displayName=ku;var Ye="MenuContent",[TS,Nu]=Nn(Ye),nh=p.forwardRef((e,t)=>{const n=eh(Ye,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=Pn(Ye,e.__scopeMenu),l=Eo(Ye,e.__scopeMenu);return d.jsx(co.Provider,{scope:e.__scopeMenu,children:d.jsx(wt,{present:r||i.open,children:d.jsx(co.Slot,{scope:e.__scopeMenu,children:l.modal?d.jsx(bS,{...o,ref:t}):d.jsx(_S,{...o,ref:t})})})})}),bS=p.forwardRef((e,t)=>{const n=Pn(Ye,e.__scopeMenu),r=p.useRef(null),o=le(t,r);return p.useEffect(()=>{const i=r.current;if(i)return I1(i)},[]),d.jsx(Pu,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:D(e.onFocusOutside,i=>i.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),_S=p.forwardRef((e,t)=>{const n=Pn(Ye,e.__scopeMenu);return d.jsx(Pu,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),MS=so("MenuContent.ScrollLock"),Pu=p.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,disableOutsidePointerEvents:s,onEntryFocus:a,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:c,onInteractOutside:m,onDismiss:w,disableOutsideScroll:x,...y}=e,S=Pn(Ye,n),v=Eo(Ye,n),h=fl(n),g=Jm(n),E=ES(n),[k,P]=p.useState(null),N=p.useRef(null),T=le(t,N,S.onContentChange),M=p.useRef(0),_=p.useRef(""),L=p.useRef(0),O=p.useRef(null),U=p.useRef("right"),j=p.useRef(0),W=x?gS:p.Fragment,F=x?{as:MS,allowPinchZoom:!0}:void 0,B=R=>{var be,pr;const A=_.current+R,z=E().filter(Ve=>!Ve.disabled),H=document.activeElement,Ee=(be=z.find(Ve=>Ve.ref.current===H))==null?void 0:be.textValue,ge=z.map(Ve=>Ve.textValue),Ot=VS(ge,A,Ee),Ce=(pr=z.find(Ve=>Ve.textValue===Ot))==null?void 0:pr.ref.current;(function Ve(mr){_.current=mr,window.clearTimeout(M.current),mr!==""&&(M.current=window.setTimeout(()=>Ve(""),1e3))})(A),Ce&&setTimeout(()=>Ce.focus())};p.useEffect(()=>()=>window.clearTimeout(M.current),[]),Lw();const C=p.useCallback(R=>{var z,H;return U.current===((z=O.current)==null?void 0:z.side)&&HS(R,(H=O.current)==null?void 0:H.area)},[]);return d.jsx(TS,{scope:n,searchRef:_,onItemEnter:p.useCallback(R=>{C(R)&&R.preventDefault()},[C]),onItemLeave:p.useCallback(R=>{var A;C(R)||((A=N.current)==null||A.focus(),P(null))},[C]),onTriggerLeave:p.useCallback(R=>{C(R)&&R.preventDefault()},[C]),pointerGraceTimerRef:L,onPointerGraceIntentChange:p.useCallback(R=>{O.current=R},[]),children:d.jsx(W,{...F,children:d.jsx(dm,{asChild:!0,trapped:o,onMountAutoFocus:D(i,R=>{var A;R.preventDefault(),(A=N.current)==null||A.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:d.jsx(fu,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:c,onInteractOutside:m,onDismiss:w,children:d.jsx(b1,{asChild:!0,...g,dir:v.dir,orientation:"vertical",loop:r,currentTabStopId:k,onCurrentTabStopIdChange:P,onEntryFocus:D(a,R=>{v.isUsingKeyboardRef.current||R.preventDefault()}),preventScrollOnEntryFocus:!0,children:d.jsx(p1,{role:"menu","aria-orientation":"vertical","data-state":yh(S.open),"data-radix-menu-content":"",dir:v.dir,...h,...y,ref:T,style:{outline:"none",...y.style},onKeyDown:D(y.onKeyDown,R=>{const z=R.target.closest("[data-radix-menu-content]")===R.currentTarget,H=R.ctrlKey||R.altKey||R.metaKey,Ee=R.key.length===1;z&&(R.key==="Tab"&&R.preventDefault(),!H&&Ee&&B(R.key));const ge=N.current;if(R.target!==ge||!wS.includes(R.key))return;R.preventDefault();const Ce=E().filter(be=>!be.disabled).map(be=>be.ref.current);Qm.includes(R.key)&&Ce.reverse(),US(Ce)}),onBlur:D(e.onBlur,R=>{R.currentTarget.contains(R.target)||(window.clearTimeout(M.current),_.current="")}),onPointerMove:D(e.onPointerMove,fo(R=>{const A=R.target,z=j.current!==R.clientX;if(R.currentTarget.contains(A)&&z){const H=R.clientX>j.current?"right":"left";U.current=H,j.current=R.clientX}}))})})})})})})});nh.displayName=Ye;var AS="MenuGroup",Ru=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return d.jsx(Y.div,{role:"group",...r,ref:t})});Ru.displayName=AS;var jS="MenuLabel",rh=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return d.jsx(Y.div,{...r,ref:t})});rh.displayName=jS;var Ui="MenuItem",Pd="menu.itemSelect",pl=p.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,i=p.useRef(null),l=Eo(Ui,e.__scopeMenu),s=Nu(Ui,e.__scopeMenu),a=le(t,i),u=p.useRef(!1),f=()=>{const c=i.current;if(!n&&c){const m=new CustomEvent(Pd,{bubbles:!0,cancelable:!0});c.addEventListener(Pd,w=>r==null?void 0:r(w),{once:!0}),uu(c,m),m.defaultPrevented?u.current=!1:l.onClose()}};return d.jsx(oh,{...o,ref:a,disabled:n,onClick:D(e.onClick,f),onPointerDown:c=>{var m;(m=e.onPointerDown)==null||m.call(e,c),u.current=!0},onPointerUp:D(e.onPointerUp,c=>{var m;u.current||(m=c.currentTarget)==null||m.click()}),onKeyDown:D(e.onKeyDown,c=>{const m=s.searchRef.current!=="";n||m&&c.key===" "||aa.includes(c.key)&&(c.currentTarget.click(),c.preventDefault())})})});pl.displayName=Ui;var oh=p.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...i}=e,l=Nu(Ui,n),s=Jm(n),a=p.useRef(null),u=le(t,a),[f,c]=p.useState(!1),[m,w]=p.useState("");return p.useEffect(()=>{const x=a.current;x&&w((x.textContent??"").trim())},[i.children]),d.jsx(co.ItemSlot,{scope:n,disabled:r,textValue:o??m,children:d.jsx(_1,{asChild:!0,...s,focusable:!r,children:d.jsx(Y.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:u,onPointerMove:D(e.onPointerMove,fo(x=>{r?l.onItemLeave(x):(l.onItemEnter(x),x.defaultPrevented||x.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:D(e.onPointerLeave,fo(x=>l.onItemLeave(x))),onFocus:D(e.onFocus,()=>c(!0)),onBlur:D(e.onBlur,()=>c(!1))})})})}),IS="MenuCheckboxItem",ih=p.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return d.jsx(ch,{scope:e.__scopeMenu,checked:n,children:d.jsx(pl,{role:"menuitemcheckbox","aria-checked":Wi(n)?"mixed":n,...o,ref:t,"data-state":bu(n),onSelect:D(o.onSelect,()=>r==null?void 0:r(Wi(n)?!0:!n),{checkForDefaultPrevented:!1})})})});ih.displayName=IS;var lh="MenuRadioGroup",[OS,LS]=Nn(lh,{value:void 0,onValueChange:()=>{}}),sh=p.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,i=ue(r);return d.jsx(OS,{scope:e.__scopeMenu,value:n,onValueChange:i,children:d.jsx(Ru,{...o,ref:t})})});sh.displayName=lh;var ah="MenuRadioItem",uh=p.forwardRef((e,t)=>{const{value:n,...r}=e,o=LS(ah,e.__scopeMenu),i=n===o.value;return d.jsx(ch,{scope:e.__scopeMenu,checked:i,children:d.jsx(pl,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":bu(i),onSelect:D(r.onSelect,()=>{var l;return(l=o.onValueChange)==null?void 0:l.call(o,n)},{checkForDefaultPrevented:!1})})})});uh.displayName=ah;var Tu="MenuItemIndicator",[ch,DS]=Nn(Tu,{checked:!1}),dh=p.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,i=DS(Tu,n);return d.jsx(wt,{present:r||Wi(i.checked)||i.checked===!0,children:d.jsx(Y.span,{...o,ref:t,"data-state":bu(i.checked)})})});dh.displayName=Tu;var FS="MenuSeparator",fh=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return d.jsx(Y.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});fh.displayName=FS;var zS="MenuArrow",ph=p.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=fl(n);return d.jsx(m1,{...o,...r,ref:t})});ph.displayName=zS;var $S="MenuSub",[bC,mh]=Nn($S),_r="MenuSubTrigger",hh=p.forwardRef((e,t)=>{const n=Pn(_r,e.__scopeMenu),r=Eo(_r,e.__scopeMenu),o=mh(_r,e.__scopeMenu),i=Nu(_r,e.__scopeMenu),l=p.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:a}=i,u={__scopeMenu:e.__scopeMenu},f=p.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return p.useEffect(()=>f,[f]),p.useEffect(()=>{const c=s.current;return()=>{window.clearTimeout(c),a(null)}},[s,a]),d.jsx(Cu,{asChild:!0,...u,children:d.jsx(oh,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":yh(n.open),...e,ref:sl(t,o.onTriggerChange),onClick:c=>{var m;(m=e.onClick)==null||m.call(e,c),!(e.disabled||c.defaultPrevented)&&(c.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:D(e.onPointerMove,fo(c=>{i.onItemEnter(c),!c.defaultPrevented&&!e.disabled&&!n.open&&!l.current&&(i.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:D(e.onPointerLeave,fo(c=>{var w,x;f();const m=(w=n.content)==null?void 0:w.getBoundingClientRect();if(m){const y=(x=n.content)==null?void 0:x.dataset.side,S=y==="right",v=S?-5:5,h=m[S?"left":"right"],g=m[S?"right":"left"];i.onPointerGraceIntentChange({area:[{x:c.clientX+v,y:c.clientY},{x:h,y:m.top},{x:g,y:m.top},{x:g,y:m.bottom},{x:h,y:m.bottom}],side:y}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(c),c.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:D(e.onKeyDown,c=>{var w;const m=i.searchRef.current!=="";e.disabled||m&&c.key===" "||xS[r.dir].includes(c.key)&&(n.onOpenChange(!0),(w=n.content)==null||w.focus(),c.preventDefault())})})})});hh.displayName=_r;var vh="MenuSubContent",gh=p.forwardRef((e,t)=>{const n=eh(Ye,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=Pn(Ye,e.__scopeMenu),l=Eo(Ye,e.__scopeMenu),s=mh(vh,e.__scopeMenu),a=p.useRef(null),u=le(t,a);return d.jsx(co.Provider,{scope:e.__scopeMenu,children:d.jsx(wt,{present:r||i.open,children:d.jsx(co.Slot,{scope:e.__scopeMenu,children:d.jsx(Pu,{id:s.contentId,"aria-labelledby":s.triggerId,...o,ref:u,align:"start",side:l.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{var c;l.isUsingKeyboardRef.current&&((c=a.current)==null||c.focus()),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:D(e.onFocusOutside,f=>{f.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:D(e.onEscapeKeyDown,f=>{l.onClose(),f.preventDefault()}),onKeyDown:D(e.onKeyDown,f=>{var w;const c=f.currentTarget.contains(f.target),m=SS[l.dir].includes(f.key);c&&m&&(i.onOpenChange(!1),(w=s.trigger)==null||w.focus(),f.preventDefault())})})})})})});gh.displayName=vh;function yh(e){return e?"open":"closed"}function Wi(e){return e==="indeterminate"}function bu(e){return Wi(e)?"indeterminate":e?"checked":"unchecked"}function US(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function WS(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function VS(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let l=WS(e,Math.max(i,0));o.length===1&&(l=l.filter(u=>u!==n));const a=l.find(u=>u.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}function BS(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,l=t.length-1;i<t.length;l=i++){const s=t[i],a=t[l],u=s.x,f=s.y,c=a.x,m=a.y;f>r!=m>r&&n<(c-u)*(r-f)/(m-f)+u&&(o=!o)}return o}function HS(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return BS(n,t)}function fo(e){return t=>t.pointerType==="mouse"?e(t):void 0}var KS=qm,GS=Cu,YS=th,XS=nh,QS=Ru,ZS=rh,JS=pl,qS=ih,eE=sh,tE=uh,nE=dh,rE=fh,oE=ph,iE=hh,lE=gh,ml="DropdownMenu",[sE,_C]=kn(ml,[Zm]),Te=Zm(),[aE,wh]=sE(ml),xh=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:i,onOpenChange:l,modal:s=!0}=e,a=Te(t),u=p.useRef(null),[f,c]=au({prop:o,defaultProp:i??!1,onChange:l,caller:ml});return d.jsx(aE,{scope:t,triggerId:na(),triggerRef:u,contentId:na(),open:f,onOpenChange:c,onOpenToggle:p.useCallback(()=>c(m=>!m),[c]),modal:s,children:d.jsx(KS,{...a,open:f,onOpenChange:c,dir:r,modal:s,children:n})})};xh.displayName=ml;var Sh="DropdownMenuTrigger",Eh=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=wh(Sh,n),l=Te(n);return d.jsx(GS,{asChild:!0,...l,children:d.jsx(Y.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:sl(t,i.triggerRef),onPointerDown:D(e.onPointerDown,s=>{!r&&s.button===0&&s.ctrlKey===!1&&(i.onOpenToggle(),i.open||s.preventDefault())}),onKeyDown:D(e.onKeyDown,s=>{r||(["Enter"," "].includes(s.key)&&i.onOpenToggle(),s.key==="ArrowDown"&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(s.key)&&s.preventDefault())})})})});Eh.displayName=Sh;var uE="DropdownMenuPortal",Ch=e=>{const{__scopeDropdownMenu:t,...n}=e,r=Te(t);return d.jsx(YS,{...r,...n})};Ch.displayName=uE;var kh="DropdownMenuContent",Nh=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=wh(kh,n),i=Te(n),l=p.useRef(!1);return d.jsx(XS,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...r,ref:t,onCloseAutoFocus:D(e.onCloseAutoFocus,s=>{var a;l.current||(a=o.triggerRef.current)==null||a.focus(),l.current=!1,s.preventDefault()}),onInteractOutside:D(e.onInteractOutside,s=>{const a=s.detail.originalEvent,u=a.button===0&&a.ctrlKey===!0,f=a.button===2||u;(!o.modal||f)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Nh.displayName=kh;var cE="DropdownMenuGroup",dE=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(QS,{...o,...r,ref:t})});dE.displayName=cE;var fE="DropdownMenuLabel",Ph=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(ZS,{...o,...r,ref:t})});Ph.displayName=fE;var pE="DropdownMenuItem",Rh=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(JS,{...o,...r,ref:t})});Rh.displayName=pE;var mE="DropdownMenuCheckboxItem",Th=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(qS,{...o,...r,ref:t})});Th.displayName=mE;var hE="DropdownMenuRadioGroup",vE=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(eE,{...o,...r,ref:t})});vE.displayName=hE;var gE="DropdownMenuRadioItem",bh=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(tE,{...o,...r,ref:t})});bh.displayName=gE;var yE="DropdownMenuItemIndicator",_h=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(nE,{...o,...r,ref:t})});_h.displayName=yE;var wE="DropdownMenuSeparator",Mh=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(rE,{...o,...r,ref:t})});Mh.displayName=wE;var xE="DropdownMenuArrow",SE=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(oE,{...o,...r,ref:t})});SE.displayName=xE;var EE="DropdownMenuSubTrigger",Ah=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(iE,{...o,...r,ref:t})});Ah.displayName=EE;var CE="DropdownMenuSubContent",jh=p.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Te(n);return d.jsx(lE,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});jh.displayName=CE;var kE=xh,NE=Eh,PE=Ch,Ih=Nh,Oh=Ph,Lh=Rh,Dh=Th,Fh=bh,zh=_h,$h=Mh,Uh=Ah,Wh=jh;const RE=kE,TE=NE,bE=p.forwardRef(({className:e,inset:t,children:n,...r},o)=>d.jsxs(Uh,{ref:o,className:Z("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[n,d.jsx(Yp,{className:"ml-auto h-4 w-4"})]}));bE.displayName=Uh.displayName;const _E=p.forwardRef(({className:e,...t},n)=>d.jsx(Wh,{ref:n,className:Z("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));_E.displayName=Wh.displayName;const Vh=p.forwardRef(({className:e,sideOffset:t=4,...n},r)=>d.jsx(PE,{children:d.jsx(Ih,{ref:r,sideOffset:t,className:Z("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));Vh.displayName=Ih.displayName;const Bh=p.forwardRef(({className:e,inset:t,...n},r)=>d.jsx(Lh,{ref:r,className:Z("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));Bh.displayName=Lh.displayName;const ME=p.forwardRef(({className:e,children:t,checked:n,...r},o)=>d.jsxs(Dh,{ref:o,className:Z("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[d.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:d.jsx(zh,{children:d.jsx(Kp,{className:"h-4 w-4"})})}),t]}));ME.displayName=Dh.displayName;const AE=p.forwardRef(({className:e,children:t,...n},r)=>d.jsxs(Fh,{ref:r,className:Z("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[d.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:d.jsx(zh,{children:d.jsx(h0,{className:"h-2 w-2 fill-current"})})}),t]}));AE.displayName=Fh.displayName;const Hh=p.forwardRef(({className:e,inset:t,...n},r)=>d.jsx(Oh,{ref:r,className:Z("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));Hh.displayName=Oh.displayName;const Kh=p.forwardRef(({className:e,...t},n)=>d.jsx($h,{ref:n,className:Z("-mx-1 my-1 h-px bg-muted",e),...t}));Kh.displayName=$h.displayName;const jE=({providers:e,selectedModel:t,onModelChange:n})=>{if(!e)return null;const r=s=>{switch(s){case"openai":return d.jsx(Bl,{className:"w-4 h-4"});case"gemini":return d.jsx(Zc,{className:"w-4 h-4"});case"openrouter":return d.jsx(qs,{className:"w-4 h-4"});case"github":return d.jsx(Qc,{className:"w-4 h-4"});default:return d.jsx(Bl,{className:"w-4 h-4"})}},o=s=>{switch(s){case"openai":return"text-green-500";case"gemini":return"text-blue-500";case"openrouter":return"text-purple-500";case"github":return"text-gray-500";default:return"text-gray-500"}},i=s=>{if(s.includes("/")){const a=s.split("/");return a[a.length-1]}return s},l=s=>s.includes(":free")||s.includes("free");return d.jsxs("div",{className:"space-y-3",children:[d.jsxs("div",{className:"flex items-center gap-2 p-3 bg-muted/50 rounded-lg",children:[d.jsx("div",{className:o(e.provider),children:r(e.provider)}),d.jsxs("div",{className:"flex-1",children:[d.jsx("div",{className:"font-medium capitalize",children:e.provider}),d.jsxs("div",{className:"text-xs text-muted-foreground",children:[e.availableModels.length," models available"]})]}),d.jsx(ct,{variant:"outline",className:"text-xs",children:"Active"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"text-sm font-medium mb-2 block",children:"Select Model"}),d.jsxs(RE,{children:[d.jsx(TE,{asChild:!0,children:d.jsxs(cn,{variant:"outline",className:"w-full justify-between",children:[d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx("span",{className:"truncate",children:i(t||e.defaultModel)}),l(t||e.defaultModel)&&d.jsx(ct,{variant:"secondary",className:"text-xs",children:"Free"})]}),d.jsx(Gp,{className:"w-4 h-4 opacity-50"})]})}),d.jsxs(Vh,{className:"w-80",children:[d.jsx(Hh,{children:"Available Models"}),d.jsx(Kh,{}),e.availableModels.map(s=>d.jsxs(Bh,{onClick:()=>n(s),className:"flex items-center justify-between cursor-pointer",children:[d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx("span",{className:"truncate",children:i(s)}),l(s)&&d.jsx(ct,{variant:"secondary",className:"text-xs",children:"Free"})]}),t===s&&d.jsx(Kp,{className:"w-4 h-4 text-primary"})]},s))]})]})]}),d.jsxs("div",{className:"text-xs text-muted-foreground space-y-1",children:[d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{children:"Max Tokens:"}),d.jsx("span",{children:e.maxTokens.toLocaleString()})]}),d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{children:"Temperature:"}),d.jsx("span",{children:e.temperature})]})]}),e.provider==="openrouter"&&d.jsxs("div",{className:"p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[d.jsx(qs,{className:"w-3 h-3 text-purple-500"}),d.jsx("span",{className:"text-xs font-medium text-purple-700 dark:text-purple-300",children:"OpenRouter"})]}),d.jsx("p",{className:"text-xs text-muted-foreground",children:"Access to multiple AI models through a unified API"})]}),e.provider==="gemini"&&d.jsxs("div",{className:"p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[d.jsx(Zc,{className:"w-3 h-3 text-blue-500"}),d.jsx("span",{className:"text-xs font-medium text-blue-700 dark:text-blue-300",children:"Google Gemini"})]}),d.jsx("p",{className:"text-xs text-muted-foreground",children:"Google's advanced AI model with multimodal capabilities"})]}),e.provider==="github"&&d.jsxs("div",{className:"p-3 bg-gray-500/10 border border-gray-500/20 rounded-lg",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[d.jsx(Qc,{className:"w-3 h-3 text-gray-500"}),d.jsx("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:"GitHub Models"})]}),d.jsx("p",{className:"text-xs text-muted-foreground",children:"AI models hosted on GitHub's infrastructure"})]}),e.provider==="openai"&&d.jsxs("div",{className:"p-3 bg-green-500/10 border border-green-500/20 rounded-lg",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[d.jsx(Bl,{className:"w-3 h-3 text-green-500"}),d.jsx("span",{className:"text-xs font-medium text-green-700 dark:text-green-300",children:"OpenAI"})]}),d.jsx("p",{className:"text-xs text-muted-foreground",children:"Industry-leading AI models from OpenAI"})]})]})};function IE(){const[e,t]=p.useState(!1),[n,r]=p.useState(null),[o,i]=p.useState(null),l=p.useRef(null),s=p.useRef(null),a=p.useRef(0),u=5,f=3e3,c=p.useCallback(()=>{try{const S=`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}`;l.current=new WebSocket(S),l.current.onopen=()=>{console.log("WebSocket connected"),t(!0),i(null),a.current=0},l.current.onmessage=v=>{try{const h=JSON.parse(v.data);r(h)}catch(h){console.error("Failed to parse WebSocket message:",h)}},l.current.onclose=v=>{console.log("WebSocket disconnected:",v.code,v.reason),t(!1),v.code!==1e3&&a.current<u?(a.current++,console.log(`Attempting to reconnect (${a.current}/${u})...`),s.current=setTimeout(()=>{c()},f*a.current)):a.current>=u&&i("Failed to reconnect to server. Please refresh the page.")},l.current.onerror=v=>{console.error("WebSocket error:",v),i("WebSocket connection error")}}catch(y){console.error("Failed to create WebSocket connection:",y),i("Failed to connect to server")}},[]),m=p.useCallback(y=>{if(l.current&&l.current.readyState===WebSocket.OPEN)try{return l.current.send(JSON.stringify(y)),!0}catch(S){return console.error("Failed to send WebSocket message:",S),i("Failed to send message"),!1}else return console.warn("WebSocket is not connected"),i("Not connected to server"),!1},[]),w=p.useCallback(()=>{s.current&&(clearTimeout(s.current),s.current=null),l.current&&(l.current.close(1e3,"User disconnected"),l.current=null),t(!1),r(null),i(null)},[]),x=p.useCallback(()=>{w(),a.current=0,c()},[c,w]);return p.useEffect(()=>(c(),()=>{w()}),[c,w]),p.useEffect(()=>{if(!e)return;const y=setInterval(()=>{l.current&&l.current.readyState===WebSocket.OPEN&&l.current.send(JSON.stringify({type:"ping"}))},3e4);return()=>clearInterval(y)},[e]),{isConnected:e,lastMessage:n,error:o,sendMessage:m,reconnect:x,disconnect:w}}const Gh=p.forwardRef(({className:e,...t},n)=>d.jsx("textarea",{className:Z("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));Gh.displayName="Textarea";function OE(e,[t,n]){return Math.min(n,Math.max(t,e))}function LE(e,t){return p.useReducer((n,r)=>t[n][r]??n,e)}var _u="ScrollArea",[Yh,MC]=kn(_u),[DE,Ze]=Yh(_u),Xh=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,type:r="hover",dir:o,scrollHideDelay:i=600,...l}=e,[s,a]=p.useState(null),[u,f]=p.useState(null),[c,m]=p.useState(null),[w,x]=p.useState(null),[y,S]=p.useState(null),[v,h]=p.useState(0),[g,E]=p.useState(0),[k,P]=p.useState(!1),[N,T]=p.useState(!1),M=le(t,L=>a(L)),_=du(o);return d.jsx(DE,{scope:n,type:r,dir:_,scrollHideDelay:i,scrollArea:s,viewport:u,onViewportChange:f,content:c,onContentChange:m,scrollbarX:w,onScrollbarXChange:x,scrollbarXEnabled:k,onScrollbarXEnabledChange:P,scrollbarY:y,onScrollbarYChange:S,scrollbarYEnabled:N,onScrollbarYEnabledChange:T,onCornerWidthChange:h,onCornerHeightChange:E,children:d.jsx(Y.div,{dir:_,...l,ref:M,style:{position:"relative","--radix-scroll-area-corner-width":v+"px","--radix-scroll-area-corner-height":g+"px",...e.style}})})});Xh.displayName=_u;var Qh="ScrollAreaViewport",Zh=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,children:r,nonce:o,...i}=e,l=Ze(Qh,n),s=p.useRef(null),a=le(t,s,l.onViewportChange);return d.jsxs(d.Fragment,{children:[d.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),d.jsx(Y.div,{"data-radix-scroll-area-viewport":"",...i,ref:a,style:{overflowX:l.scrollbarXEnabled?"scroll":"hidden",overflowY:l.scrollbarYEnabled?"scroll":"hidden",...e.style},children:d.jsx("div",{ref:l.onContentChange,style:{minWidth:"100%",display:"table"},children:r})})]})});Zh.displayName=Qh;var xt="ScrollAreaScrollbar",Mu=p.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=Ze(xt,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:l}=o,s=e.orientation==="horizontal";return p.useEffect(()=>(s?i(!0):l(!0),()=>{s?i(!1):l(!1)}),[s,i,l]),o.type==="hover"?d.jsx(FE,{...r,ref:t,forceMount:n}):o.type==="scroll"?d.jsx(zE,{...r,ref:t,forceMount:n}):o.type==="auto"?d.jsx(Jh,{...r,ref:t,forceMount:n}):o.type==="always"?d.jsx(Au,{...r,ref:t}):null});Mu.displayName=xt;var FE=p.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=Ze(xt,e.__scopeScrollArea),[i,l]=p.useState(!1);return p.useEffect(()=>{const s=o.scrollArea;let a=0;if(s){const u=()=>{window.clearTimeout(a),l(!0)},f=()=>{a=window.setTimeout(()=>l(!1),o.scrollHideDelay)};return s.addEventListener("pointerenter",u),s.addEventListener("pointerleave",f),()=>{window.clearTimeout(a),s.removeEventListener("pointerenter",u),s.removeEventListener("pointerleave",f)}}},[o.scrollArea,o.scrollHideDelay]),d.jsx(wt,{present:n||i,children:d.jsx(Jh,{"data-state":i?"visible":"hidden",...r,ref:t})})}),zE=p.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=Ze(xt,e.__scopeScrollArea),i=e.orientation==="horizontal",l=vl(()=>a("SCROLL_END"),100),[s,a]=LE("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return p.useEffect(()=>{if(s==="idle"){const u=window.setTimeout(()=>a("HIDE"),o.scrollHideDelay);return()=>window.clearTimeout(u)}},[s,o.scrollHideDelay,a]),p.useEffect(()=>{const u=o.viewport,f=i?"scrollLeft":"scrollTop";if(u){let c=u[f];const m=()=>{const w=u[f];c!==w&&(a("SCROLL"),l()),c=w};return u.addEventListener("scroll",m),()=>u.removeEventListener("scroll",m)}},[o.viewport,i,a,l]),d.jsx(wt,{present:n||s!=="hidden",children:d.jsx(Au,{"data-state":s==="hidden"?"hidden":"visible",...r,ref:t,onPointerEnter:D(e.onPointerEnter,()=>a("POINTER_ENTER")),onPointerLeave:D(e.onPointerLeave,()=>a("POINTER_LEAVE"))})})}),Jh=p.forwardRef((e,t)=>{const n=Ze(xt,e.__scopeScrollArea),{forceMount:r,...o}=e,[i,l]=p.useState(!1),s=e.orientation==="horizontal",a=vl(()=>{if(n.viewport){const u=n.viewport.offsetWidth<n.viewport.scrollWidth,f=n.viewport.offsetHeight<n.viewport.scrollHeight;l(s?u:f)}},10);return lr(n.viewport,a),lr(n.content,a),d.jsx(wt,{present:r||i,children:d.jsx(Au,{"data-state":i?"visible":"hidden",...o,ref:t})})}),Au=p.forwardRef((e,t)=>{const{orientation:n="vertical",...r}=e,o=Ze(xt,e.__scopeScrollArea),i=p.useRef(null),l=p.useRef(0),[s,a]=p.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=rv(s.viewport,s.content),f={...r,sizes:s,onSizesChange:a,hasThumb:u>0&&u<1,onThumbChange:m=>i.current=m,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:m=>l.current=m};function c(m,w){return HE(m,l.current,s,w)}return n==="horizontal"?d.jsx($E,{...f,ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){const m=o.viewport.scrollLeft,w=Rd(m,s,o.dir);i.current.style.transform=`translate3d(${w}px, 0, 0)`}},onWheelScroll:m=>{o.viewport&&(o.viewport.scrollLeft=m)},onDragScroll:m=>{o.viewport&&(o.viewport.scrollLeft=c(m,o.dir))}}):n==="vertical"?d.jsx(UE,{...f,ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){const m=o.viewport.scrollTop,w=Rd(m,s);i.current.style.transform=`translate3d(0, ${w}px, 0)`}},onWheelScroll:m=>{o.viewport&&(o.viewport.scrollTop=m)},onDragScroll:m=>{o.viewport&&(o.viewport.scrollTop=c(m))}}):null}),$E=p.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...o}=e,i=Ze(xt,e.__scopeScrollArea),[l,s]=p.useState(),a=p.useRef(null),u=le(t,a,i.onScrollbarXChange);return p.useEffect(()=>{a.current&&s(getComputedStyle(a.current))},[a]),d.jsx(ev,{"data-orientation":"horizontal",...o,ref:u,sizes:n,style:{bottom:0,left:i.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:i.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":hl(n)+"px",...e.style},onThumbPointerDown:f=>e.onThumbPointerDown(f.x),onDragScroll:f=>e.onDragScroll(f.x),onWheelScroll:(f,c)=>{if(i.viewport){const m=i.viewport.scrollLeft+f.deltaX;e.onWheelScroll(m),iv(m,c)&&f.preventDefault()}},onResize:()=>{a.current&&i.viewport&&l&&r({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:a.current.clientWidth,paddingStart:Bi(l.paddingLeft),paddingEnd:Bi(l.paddingRight)}})}})}),UE=p.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...o}=e,i=Ze(xt,e.__scopeScrollArea),[l,s]=p.useState(),a=p.useRef(null),u=le(t,a,i.onScrollbarYChange);return p.useEffect(()=>{a.current&&s(getComputedStyle(a.current))},[a]),d.jsx(ev,{"data-orientation":"vertical",...o,ref:u,sizes:n,style:{top:0,right:i.dir==="ltr"?0:void 0,left:i.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":hl(n)+"px",...e.style},onThumbPointerDown:f=>e.onThumbPointerDown(f.y),onDragScroll:f=>e.onDragScroll(f.y),onWheelScroll:(f,c)=>{if(i.viewport){const m=i.viewport.scrollTop+f.deltaY;e.onWheelScroll(m),iv(m,c)&&f.preventDefault()}},onResize:()=>{a.current&&i.viewport&&l&&r({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:a.current.clientHeight,paddingStart:Bi(l.paddingTop),paddingEnd:Bi(l.paddingBottom)}})}})}),[WE,qh]=Yh(xt),ev=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,sizes:r,hasThumb:o,onThumbChange:i,onThumbPointerUp:l,onThumbPointerDown:s,onThumbPositionChange:a,onDragScroll:u,onWheelScroll:f,onResize:c,...m}=e,w=Ze(xt,n),[x,y]=p.useState(null),S=le(t,M=>y(M)),v=p.useRef(null),h=p.useRef(""),g=w.viewport,E=r.content-r.viewport,k=ue(f),P=ue(a),N=vl(c,10);function T(M){if(v.current){const _=M.clientX-v.current.left,L=M.clientY-v.current.top;u({x:_,y:L})}}return p.useEffect(()=>{const M=_=>{const L=_.target;(x==null?void 0:x.contains(L))&&k(_,E)};return document.addEventListener("wheel",M,{passive:!1}),()=>document.removeEventListener("wheel",M,{passive:!1})},[g,x,E,k]),p.useEffect(P,[r,P]),lr(x,N),lr(w.content,N),d.jsx(WE,{scope:n,scrollbar:x,hasThumb:o,onThumbChange:ue(i),onThumbPointerUp:ue(l),onThumbPositionChange:P,onThumbPointerDown:ue(s),children:d.jsx(Y.div,{...m,ref:S,style:{position:"absolute",...m.style},onPointerDown:D(e.onPointerDown,M=>{M.button===0&&(M.target.setPointerCapture(M.pointerId),v.current=x.getBoundingClientRect(),h.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",w.viewport&&(w.viewport.style.scrollBehavior="auto"),T(M))}),onPointerMove:D(e.onPointerMove,T),onPointerUp:D(e.onPointerUp,M=>{const _=M.target;_.hasPointerCapture(M.pointerId)&&_.releasePointerCapture(M.pointerId),document.body.style.webkitUserSelect=h.current,w.viewport&&(w.viewport.style.scrollBehavior=""),v.current=null})})})}),Vi="ScrollAreaThumb",tv=p.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=qh(Vi,e.__scopeScrollArea);return d.jsx(wt,{present:n||o.hasThumb,children:d.jsx(VE,{ref:t,...r})})}),VE=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,style:r,...o}=e,i=Ze(Vi,n),l=qh(Vi,n),{onThumbPositionChange:s}=l,a=le(t,c=>l.onThumbChange(c)),u=p.useRef(void 0),f=vl(()=>{u.current&&(u.current(),u.current=void 0)},100);return p.useEffect(()=>{const c=i.viewport;if(c){const m=()=>{if(f(),!u.current){const w=KE(c,s);u.current=w,s()}};return s(),c.addEventListener("scroll",m),()=>c.removeEventListener("scroll",m)}},[i.viewport,f,s]),d.jsx(Y.div,{"data-state":l.hasThumb?"visible":"hidden",...o,ref:a,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...r},onPointerDownCapture:D(e.onPointerDownCapture,c=>{const w=c.target.getBoundingClientRect(),x=c.clientX-w.left,y=c.clientY-w.top;l.onThumbPointerDown({x,y})}),onPointerUp:D(e.onPointerUp,l.onThumbPointerUp)})});tv.displayName=Vi;var ju="ScrollAreaCorner",nv=p.forwardRef((e,t)=>{const n=Ze(ju,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return n.type!=="scroll"&&r?d.jsx(BE,{...e,ref:t}):null});nv.displayName=ju;var BE=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,...r}=e,o=Ze(ju,n),[i,l]=p.useState(0),[s,a]=p.useState(0),u=!!(i&&s);return lr(o.scrollbarX,()=>{var c;const f=((c=o.scrollbarX)==null?void 0:c.offsetHeight)||0;o.onCornerHeightChange(f),a(f)}),lr(o.scrollbarY,()=>{var c;const f=((c=o.scrollbarY)==null?void 0:c.offsetWidth)||0;o.onCornerWidthChange(f),l(f)}),u?d.jsx(Y.div,{...r,ref:t,style:{width:i,height:s,position:"absolute",right:o.dir==="ltr"?0:void 0,left:o.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function Bi(e){return e?parseInt(e,10):0}function rv(e,t){const n=e/t;return isNaN(n)?0:n}function hl(e){const t=rv(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,r=(e.scrollbar.size-n)*t;return Math.max(r,18)}function HE(e,t,n,r="ltr"){const o=hl(n),i=o/2,l=t||i,s=o-l,a=n.scrollbar.paddingStart+l,u=n.scrollbar.size-n.scrollbar.paddingEnd-s,f=n.content-n.viewport,c=r==="ltr"?[0,f]:[f*-1,0];return ov([a,u],c)(e)}function Rd(e,t,n="ltr"){const r=hl(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,l=t.content-t.viewport,s=i-r,a=n==="ltr"?[0,l]:[l*-1,0],u=OE(e,a);return ov([0,l],[0,s])(u)}function ov(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function iv(e,t){return e>0&&e<t}var KE=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return function o(){const i={left:e.scrollLeft,top:e.scrollTop},l=n.left!==i.left,s=n.top!==i.top;(l||s)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function vl(e,t){const n=ue(e),r=p.useRef(0);return p.useEffect(()=>()=>window.clearTimeout(r.current),[]),p.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(n,t)},[n,t])}function lr(e,t){const n=ue(t);vt(()=>{let r=0;if(e){const o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(n)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,n])}var lv=Xh,GE=Zh,YE=nv;const ci=p.forwardRef(({className:e,children:t,...n},r)=>d.jsxs(lv,{ref:r,className:Z("relative overflow-hidden",e),...n,children:[d.jsx(GE,{className:"h-full w-full rounded-[inherit]",children:t}),d.jsx(sv,{}),d.jsx(YE,{})]}));ci.displayName=lv.displayName;const sv=p.forwardRef(({className:e,orientation:t="vertical",...n},r)=>d.jsx(Mu,{ref:r,orientation:t,className:Z("flex touch-none select-none transition-colors",t==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",t==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...n,children:d.jsx(tv,{className:"relative flex-1 rounded-full bg-border"})}));sv.displayName=Mu.displayName;var XE="Separator",Td="horizontal",QE=["horizontal","vertical"],av=p.forwardRef((e,t)=>{const{decorative:n,orientation:r=Td,...o}=e,i=ZE(r)?r:Td,s=n?{role:"none"}:{"aria-orientation":i==="vertical"?i:void 0,role:"separator"};return d.jsx(Y.div,{"data-orientation":i,...s,...o,ref:t})});av.displayName=XE;function ZE(e){return QE.includes(e)}var uv=av;const JE=p.forwardRef(({className:e,orientation:t="horizontal",decorative:n=!0,...r},o)=>d.jsx(uv,{ref:o,decorative:n,orientation:t,className:Z("shrink-0 bg-border",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...r}));JE.displayName=uv.displayName;var qE=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),eC="VisuallyHidden",Iu=p.forwardRef((e,t)=>d.jsx(Y.span,{...e,ref:t,style:{...qE,...e.style}}));Iu.displayName=eC;var Ou="ToastProvider",[Lu,tC,nC]=cu("Toast"),[cv,AC]=kn("Toast",[nC]),[rC,gl]=cv(Ou),dv=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:l}=e,[s,a]=p.useState(null),[u,f]=p.useState(0),c=p.useRef(!1),m=p.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Ou}\`. Expected non-empty \`string\`.`),d.jsx(Lu.Provider,{scope:t,children:d.jsx(rC,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:s,onViewportChange:a,onToastAdd:p.useCallback(()=>f(w=>w+1),[]),onToastRemove:p.useCallback(()=>f(w=>w-1),[]),isFocusedToastEscapeKeyDownRef:c,isClosePausedRef:m,children:l})})};dv.displayName=Ou;var fv="ToastViewport",oC=["F8"],ua="toast.viewportPause",ca="toast.viewportResume",pv=p.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=oC,label:o="Notifications ({hotkey})",...i}=e,l=gl(fv,n),s=tC(n),a=p.useRef(null),u=p.useRef(null),f=p.useRef(null),c=p.useRef(null),m=le(t,c,l.onViewportChange),w=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=l.toastCount>0;p.useEffect(()=>{const S=v=>{var g;r.length!==0&&r.every(E=>v[E]||v.code===E)&&((g=c.current)==null||g.focus())};return document.addEventListener("keydown",S),()=>document.removeEventListener("keydown",S)},[r]),p.useEffect(()=>{const S=a.current,v=c.current;if(x&&S&&v){const h=()=>{if(!l.isClosePausedRef.current){const P=new CustomEvent(ua);v.dispatchEvent(P),l.isClosePausedRef.current=!0}},g=()=>{if(l.isClosePausedRef.current){const P=new CustomEvent(ca);v.dispatchEvent(P),l.isClosePausedRef.current=!1}},E=P=>{!S.contains(P.relatedTarget)&&g()},k=()=>{S.contains(document.activeElement)||g()};return S.addEventListener("focusin",h),S.addEventListener("focusout",E),S.addEventListener("pointermove",h),S.addEventListener("pointerleave",k),window.addEventListener("blur",h),window.addEventListener("focus",g),()=>{S.removeEventListener("focusin",h),S.removeEventListener("focusout",E),S.removeEventListener("pointermove",h),S.removeEventListener("pointerleave",k),window.removeEventListener("blur",h),window.removeEventListener("focus",g)}}},[x,l.isClosePausedRef]);const y=p.useCallback(({tabbingDirection:S})=>{const h=s().map(g=>{const E=g.ref.current,k=[E,...gC(E)];return S==="forwards"?k:k.reverse()});return(S==="forwards"?h.reverse():h).flat()},[s]);return p.useEffect(()=>{const S=c.current;if(S){const v=h=>{var k,P,N;const g=h.altKey||h.ctrlKey||h.metaKey;if(h.key==="Tab"&&!g){const T=document.activeElement,M=h.shiftKey;if(h.target===S&&M){(k=u.current)==null||k.focus();return}const O=y({tabbingDirection:M?"backwards":"forwards"}),U=O.findIndex(j=>j===T);ns(O.slice(U+1))?h.preventDefault():M?(P=u.current)==null||P.focus():(N=f.current)==null||N.focus()}};return S.addEventListener("keydown",v),()=>S.removeEventListener("keydown",v)}},[s,y]),d.jsxs(Ow,{ref:a,role:"region","aria-label":o.replace("{hotkey}",w),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&d.jsx(da,{ref:u,onFocusFromOutsideViewport:()=>{const S=y({tabbingDirection:"forwards"});ns(S)}}),d.jsx(Lu.Slot,{scope:n,children:d.jsx(Y.ol,{tabIndex:-1,...i,ref:m})}),x&&d.jsx(da,{ref:f,onFocusFromOutsideViewport:()=>{const S=y({tabbingDirection:"backwards"});ns(S)}})]})});pv.displayName=fv;var mv="ToastFocusProxy",da=p.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=gl(mv,n);return d.jsx(Iu,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:l=>{var u;const s=l.relatedTarget;!((u=i.viewport)!=null&&u.contains(s))&&r()}})});da.displayName=mv;var Co="Toast",iC="toast.swipeStart",lC="toast.swipeMove",sC="toast.swipeCancel",aC="toast.swipeEnd",hv=p.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...l}=e,[s,a]=au({prop:r,defaultProp:o??!0,onChange:i,caller:Co});return d.jsx(wt,{present:n||s,children:d.jsx(dC,{open:s,...l,ref:t,onClose:()=>a(!1),onPause:ue(e.onPause),onResume:ue(e.onResume),onSwipeStart:D(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:D(e.onSwipeMove,u=>{const{x:f,y:c}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${c}px`)}),onSwipeCancel:D(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:D(e.onSwipeEnd,u=>{const{x:f,y:c}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${c}px`),a(!1)})})})});hv.displayName=Co;var[uC,cC]=cv(Co,{onClose(){}}),dC=p.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:l,onEscapeKeyDown:s,onPause:a,onResume:u,onSwipeStart:f,onSwipeMove:c,onSwipeCancel:m,onSwipeEnd:w,...x}=e,y=gl(Co,n),[S,v]=p.useState(null),h=le(t,j=>v(j)),g=p.useRef(null),E=p.useRef(null),k=o||y.duration,P=p.useRef(0),N=p.useRef(k),T=p.useRef(0),{onToastAdd:M,onToastRemove:_}=y,L=ue(()=>{var W;(S==null?void 0:S.contains(document.activeElement))&&((W=y.viewport)==null||W.focus()),l()}),O=p.useCallback(j=>{!j||j===1/0||(window.clearTimeout(T.current),P.current=new Date().getTime(),T.current=window.setTimeout(L,j))},[L]);p.useEffect(()=>{const j=y.viewport;if(j){const W=()=>{O(N.current),u==null||u()},F=()=>{const B=new Date().getTime()-P.current;N.current=N.current-B,window.clearTimeout(T.current),a==null||a()};return j.addEventListener(ua,F),j.addEventListener(ca,W),()=>{j.removeEventListener(ua,F),j.removeEventListener(ca,W)}}},[y.viewport,k,a,u,O]),p.useEffect(()=>{i&&!y.isClosePausedRef.current&&O(k)},[i,k,y.isClosePausedRef,O]),p.useEffect(()=>(M(),()=>_()),[M,_]);const U=p.useMemo(()=>S?Ev(S):null,[S]);return y.viewport?d.jsxs(d.Fragment,{children:[U&&d.jsx(fC,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:U}),d.jsx(uC,{scope:n,onClose:L,children:yo.createPortal(d.jsx(Lu.ItemSlot,{scope:n,children:d.jsx(Iw,{asChild:!0,onEscapeKeyDown:D(s,()=>{y.isFocusedToastEscapeKeyDownRef.current||L(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:d.jsx(Y.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":y.swipeDirection,...x,ref:h,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:D(e.onKeyDown,j=>{j.key==="Escape"&&(s==null||s(j.nativeEvent),j.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,L()))}),onPointerDown:D(e.onPointerDown,j=>{j.button===0&&(g.current={x:j.clientX,y:j.clientY})}),onPointerMove:D(e.onPointerMove,j=>{if(!g.current)return;const W=j.clientX-g.current.x,F=j.clientY-g.current.y,B=!!E.current,C=["left","right"].includes(y.swipeDirection),R=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,A=C?R(0,W):0,z=C?0:R(0,F),H=j.pointerType==="touch"?10:2,Ee={x:A,y:z},ge={originalEvent:j,delta:Ee};B?(E.current=Ee,Yo(lC,c,ge,{discrete:!1})):bd(Ee,y.swipeDirection,H)?(E.current=Ee,Yo(iC,f,ge,{discrete:!1}),j.target.setPointerCapture(j.pointerId)):(Math.abs(W)>H||Math.abs(F)>H)&&(g.current=null)}),onPointerUp:D(e.onPointerUp,j=>{const W=E.current,F=j.target;if(F.hasPointerCapture(j.pointerId)&&F.releasePointerCapture(j.pointerId),E.current=null,g.current=null,W){const B=j.currentTarget,C={originalEvent:j,delta:W};bd(W,y.swipeDirection,y.swipeThreshold)?Yo(aC,w,C,{discrete:!0}):Yo(sC,m,C,{discrete:!0}),B.addEventListener("click",R=>R.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),fC=e=>{const{__scopeToast:t,children:n,...r}=e,o=gl(Co,t),[i,l]=p.useState(!1),[s,a]=p.useState(!1);return hC(()=>l(!0)),p.useEffect(()=>{const u=window.setTimeout(()=>a(!0),1e3);return()=>window.clearTimeout(u)},[]),s?null:d.jsx(Eu,{asChild:!0,children:d.jsx(Iu,{...r,children:i&&d.jsxs(d.Fragment,{children:[o.label," ",n]})})})},pC="ToastTitle",vv=p.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return d.jsx(Y.div,{...r,ref:t})});vv.displayName=pC;var mC="ToastDescription",gv=p.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return d.jsx(Y.div,{...r,ref:t})});gv.displayName=mC;var yv="ToastAction",wv=p.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?d.jsx(Sv,{altText:n,asChild:!0,children:d.jsx(Du,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${yv}\`. Expected non-empty \`string\`.`),null)});wv.displayName=yv;var xv="ToastClose",Du=p.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=cC(xv,n);return d.jsx(Sv,{asChild:!0,children:d.jsx(Y.button,{type:"button",...r,ref:t,onClick:D(e.onClick,o.onClose)})})});Du.displayName=xv;var Sv=p.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return d.jsx(Y.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Ev(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),vC(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const l=r.dataset.radixToastAnnounceAlt;l&&t.push(l)}else t.push(...Ev(r))}}),t}function Yo(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?uu(o,i):o.dispatchEvent(i)}var bd=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function hC(e=()=>{}){const t=ue(e);vt(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function vC(e){return e.nodeType===e.ELEMENT_NODE}function gC(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ns(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var yC=dv,Cv=pv,kv=hv,Nv=vv,Pv=gv,Rv=wv,Tv=Du;const wC=yC,bv=p.forwardRef(({className:e,...t},n)=>d.jsx(Cv,{ref:n,className:Z("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));bv.displayName=Cv.displayName;const xC=su("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),_v=p.forwardRef(({className:e,variant:t,...n},r)=>d.jsx(kv,{ref:r,className:Z(xC({variant:t}),e),...n}));_v.displayName=kv.displayName;const SC=p.forwardRef(({className:e,...t},n)=>d.jsx(Rv,{ref:n,className:Z("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));SC.displayName=Rv.displayName;const Mv=p.forwardRef(({className:e,...t},n)=>d.jsx(Tv,{ref:n,className:Z("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:d.jsx(R0,{className:"h-4 w-4"})}));Mv.displayName=Tv.displayName;const Av=p.forwardRef(({className:e,...t},n)=>d.jsx(Nv,{ref:n,className:Z("text-sm font-semibold",e),...t}));Av.displayName=Nv.displayName;const jv=p.forwardRef(({className:e,...t},n)=>d.jsx(Pv,{ref:n,className:Z("text-sm opacity-90",e),...t}));jv.displayName=Pv.displayName;const EC=1,CC=1e6;let rs=0;function kC(){return rs=(rs+1)%Number.MAX_SAFE_INTEGER,rs.toString()}const os=new Map,_d=e=>{if(os.has(e))return;const t=setTimeout(()=>{os.delete(e),$r({type:"REMOVE_TOAST",toastId:e})},CC);os.set(e,t)},NC=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,EC)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?_d(n):e.toasts.forEach(r=>{_d(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},di=[];let fi={toasts:[]};function $r(e){fi=NC(fi,e),di.forEach(t=>{t(fi)})}function PC({...e}){const t=kC(),n=o=>$r({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>$r({type:"DISMISS_TOAST",toastId:t});return $r({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function Iv(){const[e,t]=p.useState(fi);return p.useEffect(()=>(di.push(t),()=>{const n=di.indexOf(t);n>-1&&di.splice(n,1)}),[e]),{...e,toast:PC,dismiss:n=>$r({type:"DISMISS_TOAST",toastId:n})}}function RC(){const{toasts:e}=Iv();return d.jsxs(wC,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return d.jsxs(_v,{...i,children:[d.jsxs("div",{className:"grid gap-1",children:[n&&d.jsx(Av,{children:n}),r&&d.jsx(jv,{children:r})]}),o,d.jsx(Mv,{})]},t)}),d.jsx(bv,{})]})}function TC(){const[e,t]=p.useState([{id:"welcome",type:"system",content:"Welcome to ChatProj! I'm your AI coding assistant. How can I help you with your project today?",timestamp:new Date}]),[n,r]=p.useState(""),[o,i]=p.useState(!1),[l,s]=p.useState(null),[a,u]=p.useState(null),[f,c]=p.useState([]),[m,w]=p.useState(null),[x,y]=p.useState(null),[S,v]=p.useState(null),[h,g]=p.useState(!1),E=p.useRef(null),k=p.useRef(null),P=p.useRef(Date.now().toString()),{isConnected:N,sendMessage:T,lastMessage:M}=IE(),{toast:_}=Iv();p.useEffect(()=>{N&&fetch("/api/ai-providers").then(C=>C.json()).then(C=>{y(C),v(C.defaultModel)}).catch(C=>{console.error("Failed to fetch AI providers:",C),_({title:"Error",description:"Failed to load AI provider information",variant:"destructive"})})},[N,_]),p.useEffect(()=>{if(!M)return;const C=M;switch(C.type){case"project-info":s(C.data);break;case"file-tree":u(C.data);break;case"ai-response-chunk":L(C);break;case"action-result":O(C);break;case"action-error":U(C);break;case"error":j(`Error: ${C.error}`),i(!1);break}},[M]),p.useEffect(()=>{var C;(C=E.current)==null||C.scrollIntoView({behavior:"smooth"})},[e]);const L=C=>{C.isComplete?(i(!1),C.actions&&C.actions.length>0&&c(R=>[...R,...C.actions.map(A=>({...A,id:Date.now()+Math.random(),status:"pending",timestamp:new Date}))])):C.content&&t(R=>{const A=R[R.length-1];return A&&A.type==="ai"&&A.streaming?[...R.slice(0,-1),{...A,content:A.content+C.content}]:[...R,{id:C.conversationId+"-ai",type:"ai",content:C.content,streaming:!0,timestamp:new Date}]})},O=C=>{c(R=>R.map(A=>A.id===C.action.id?{...A,status:"success",result:C.result}:A)),j(`✅ ${C.action.type}: ${C.action.payload.path||C.action.payload.command}`)},U=C=>{c(R=>R.map(A=>A.id===C.action.id?{...A,status:"error",error:C.error}:A)),j(`❌ ${C.action.type} failed: ${C.error}`)},j=C=>{t(R=>[...R,{id:Date.now().toString(),type:"system",content:C,timestamp:new Date}])},W=()=>{const C=n.trim();!C||!N||o||(t(R=>[...R,{id:Date.now().toString(),type:"user",content:C,timestamp:new Date}]),r(""),i(!0),T({type:"chat-message",payload:{message:C,conversationId:P.current,model:S}}))},F=C=>{C.key==="Enter"&&!C.shiftKey&&(C.preventDefault(),W())},B=C=>{w(C)};return d.jsxs("div",{className:"flex h-screen bg-background text-foreground",children:[d.jsxs("div",{className:"w-80 border-r bg-card flex flex-col",children:[d.jsxs("div",{className:"p-6 border-b",children:[d.jsxs("div",{className:"flex items-center justify-between mb-4",children:[d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx("div",{className:"p-2 bg-primary rounded-lg",children:d.jsx(qs,{className:"w-5 h-5 text-primary-foreground"})}),d.jsxs("div",{children:[d.jsx("h1",{className:"text-xl font-bold",children:"ChatProj"}),d.jsx("p",{className:"text-xs text-muted-foreground",children:"AI Coding Assistant"})]})]}),d.jsx(cn,{variant:"ghost",size:"icon",onClick:()=>g(!h),children:d.jsx(Zp,{className:"w-4 h-4"})})]}),l&&d.jsx(io,{className:"mb-4",children:d.jsxs(lo,{className:"p-4",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[d.jsx(Qp,{className:"w-4 h-4 text-muted-foreground"}),d.jsx("span",{className:"font-medium",children:l.name})]}),d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(ct,{variant:"secondary",className:"text-xs",children:l.type}),l.version&&d.jsxs(ct,{variant:"outline",className:"text-xs",children:["v",l.version]})]})]})}),h&&x&&d.jsxs(io,{className:"mb-4",children:[d.jsx(lm,{className:"pb-3",children:d.jsxs(sm,{className:"text-sm flex items-center gap-2",children:[d.jsx(Js,{className:"w-4 h-4"}),"AI Model"]})}),d.jsx(lo,{className:"pt-0",children:d.jsx(jE,{providers:x,selectedModel:S,onModelChange:v})})]})]}),d.jsx("div",{className:"flex-1 overflow-hidden",children:d.jsxs("div",{className:"p-4 h-full flex flex-col",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[d.jsx(oo,{className:"w-4 h-4 text-muted-foreground"}),d.jsx("h3",{className:"text-sm font-medium",children:"Project Files"})]}),d.jsx(ci,{className:"flex-1",children:a?d.jsx(fw,{tree:a,onFileSelect:B,selectedFile:m}):d.jsx("div",{className:"text-sm text-muted-foreground",children:"Loading..."})})]})}),d.jsx("div",{className:"p-4 border-t",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[d.jsx("div",{className:`w-2 h-2 rounded-full ${N?"bg-green-500":"bg-destructive"}`}),d.jsx("span",{className:"text-muted-foreground",children:N?"Connected":"Disconnected"})]}),x&&d.jsx(ct,{variant:"outline",className:"text-xs",children:x.provider})]})})]}),d.jsxs("div",{className:"flex-1 flex flex-col bg-background",children:[d.jsx("div",{className:"border-b p-4",children:d.jsxs("div",{className:"max-w-4xl mx-auto flex items-center justify-between",children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:d.jsx(Js,{className:"w-5 h-5 text-primary"})}),d.jsxs("div",{children:[d.jsx("h2",{className:"font-semibold",children:"AI Assistant"}),d.jsx("p",{className:"text-sm text-muted-foreground",children:S?`Using ${S}`:"Ready to help"})]})]}),o&&d.jsx(ct,{variant:"secondary",className:"animate-pulse",children:"Thinking..."})]})}),d.jsx(ci,{className:"flex-1 p-6",children:d.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[e.map(C=>d.jsx(dw,{message:C},C.id)),d.jsx("div",{ref:E})]})}),f.length>0&&d.jsx("div",{className:"border-t p-4 bg-muted/30",children:d.jsxs("div",{className:"max-w-4xl mx-auto",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[d.jsx(Jp,{className:"w-4 h-4 text-muted-foreground"}),d.jsx("h3",{className:"text-sm font-medium",children:"Recent Actions"}),d.jsx(ct,{variant:"secondary",className:"text-xs",children:f.length})]}),d.jsx(ci,{className:"max-h-32",children:d.jsx("div",{className:"space-y-2",children:f.slice(-5).map(C=>d.jsx(pw,{action:C},C.id))})})]})}),d.jsx("div",{className:"border-t p-6 bg-background",children:d.jsxs("div",{className:"max-w-4xl mx-auto",children:[d.jsxs("div",{className:"flex gap-4",children:[d.jsxs("div",{className:"flex-1 relative",children:[d.jsx(Gh,{ref:k,value:n,onChange:C=>r(C.target.value),onKeyDown:F,placeholder:"Ask me anything about your project...",className:"min-h-[60px] max-h-[150px] resize-none pr-12",disabled:!N||o}),n&&d.jsxs("div",{className:"absolute bottom-3 right-3 text-xs text-muted-foreground",children:[n.length,"/2000"]})]}),d.jsxs(cn,{onClick:W,disabled:!n.trim()||!N||o,size:"lg",className:"px-6",children:[d.jsx(C0,{className:"w-4 h-4 mr-2"}),"Send"]})]}),d.jsxs("div",{className:"flex gap-2 mt-3",children:[d.jsx(cn,{variant:"outline",size:"sm",onClick:()=>r("What files are in this project?"),disabled:o,children:"📁 Explore Files"}),d.jsx(cn,{variant:"outline",size:"sm",onClick:()=>r("Create a simple React component"),disabled:o,children:"⚛️ Create Component"}),d.jsx(cn,{variant:"outline",size:"sm",onClick:()=>r("Help me debug this code"),disabled:o,children:"🐛 Debug Code"})]})]})})]}),d.jsx(RC,{})]})}is.createRoot(document.getElementById("root")).render(d.jsx(Et.StrictMode,{children:d.jsx(TC,{})}));
