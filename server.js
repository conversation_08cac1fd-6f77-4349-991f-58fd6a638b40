const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const fs = require('fs');
const cors = require('cors');
const helmet = require('helmet');
const chokidar = require('chokidar');
require('dotenv').config();

const FileSystemManager = require('./lib/fileSystemManager');
const AIManager = require('./lib/openaiManager');
const ActionParser = require('./lib/actionParser');
const SecurityManager = require('./lib/securityManager');

class ChatProjServer {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = new WebSocket.Server({ server: this.server });
    
    this.port = process.env.PORT || 3000;
    this.projectPath = process.env.PROJECT_PATH || process.cwd();
    
    this.fileSystemManager = new FileSystemManager(this.projectPath);
    this.aiManager = new AIManager();
    this.actionParser = new ActionParser();
    this.securityManager = new SecurityManager(this.projectPath);
    
    this.clients = new Set();
    this.fileWatcher = null;
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
    this.setupFileWatcher();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "blob:"],
          connectSrc: ["'self'", "ws:", "wss:"],
        },
      },
    }));

    this.app.use(cors({
      origin: `http://localhost:${this.port}`,
      credentials: true
    }));

    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Serve static files from client build
    const clientBuildPath = path.join(__dirname, 'client', 'dist');
    if (fs.existsSync(clientBuildPath)) {
      this.app.use(express.static(clientBuildPath));
    } else {
      // Development mode - serve from public directory
      const publicPath = path.join(__dirname, 'public');
      this.app.use(express.static(publicPath));
    }
  }

  setupRoutes() {
    // API Routes
    this.app.get('/api/health', (req, res) => {
      res.json({
        status: 'ok',
        projectPath: this.projectPath,
        timestamp: new Date().toISOString()
      });
    });

    this.app.get('/api/ai-providers', (req, res) => {
      try {
        const providerInfo = this.aiManager.getProviderInfo();
        res.json(providerInfo);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/api/project-info', (req, res) => {
      try {
        const projectInfo = this.fileSystemManager.getProjectInfo();
        res.json(projectInfo);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/api/file-tree', (req, res) => {
      try {
        const fileTree = this.fileSystemManager.getFileTree();
        res.json(fileTree);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/api/file/:path(*)', (req, res) => {
      try {
        const filePath = req.params.path;
        if (!this.securityManager.isPathSafe(filePath)) {
          return res.status(403).json({ error: 'Access denied' });
        }
        
        const content = this.fileSystemManager.readFile(filePath);
        res.json({ content, path: filePath });
      } catch (error) {
        res.status(404).json({ error: error.message });
      }
    });

    this.app.post('/api/file/:path(*)', (req, res) => {
      try {
        const filePath = req.params.path;
        const { content } = req.body;
        
        if (!this.securityManager.isPathSafe(filePath)) {
          return res.status(403).json({ error: 'Access denied' });
        }
        
        this.fileSystemManager.writeFile(filePath, content);
        res.json({ success: true, path: filePath });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.delete('/api/file/:path(*)', (req, res) => {
      try {
        const filePath = req.params.path;
        
        if (!this.securityManager.isPathSafe(filePath)) {
          return res.status(403).json({ error: 'Access denied' });
        }
        
        this.fileSystemManager.deleteFile(filePath);
        res.json({ success: true, path: filePath });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Catch-all handler for SPA
    this.app.get('*', (req, res) => {
      const clientBuildPath = path.join(__dirname, 'client', 'dist');
      const indexPath = fs.existsSync(clientBuildPath) 
        ? path.join(clientBuildPath, 'index.html')
        : path.join(__dirname, 'public', 'index.html');
      
      if (fs.existsSync(indexPath)) {
        res.sendFile(indexPath);
      } else {
        res.status(404).send('ChatProj client not found. Please run "npm run build" first.');
      }
    });
  }

  setupWebSocket() {
    this.wss.on('connection', (ws) => {
      console.log('Client connected');
      this.clients.add(ws);

      ws.on('message', async (message) => {
        try {
          const data = JSON.parse(message);
          await this.handleWebSocketMessage(ws, data);
        } catch (error) {
          console.error('WebSocket message error:', error);
          ws.send(JSON.stringify({
            type: 'error',
            error: error.message
          }));
        }
      });

      ws.on('close', () => {
        console.log('Client disconnected');
        this.clients.delete(ws);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.clients.delete(ws);
      });

      // Send initial project info
      ws.send(JSON.stringify({
        type: 'project-info',
        data: this.fileSystemManager.getProjectInfo()
      }));
    });
  }

  async handleWebSocketMessage(ws, data) {
    const { type, payload } = data;

    switch (type) {
      case 'chat-message':
        await this.handleChatMessage(ws, payload);
        break;
      
      case 'file-operation':
        await this.handleFileOperation(ws, payload);
        break;
      
      case 'get-file-tree':
        ws.send(JSON.stringify({
          type: 'file-tree',
          data: this.fileSystemManager.getFileTree()
        }));
        break;
      
      default:
        ws.send(JSON.stringify({
          type: 'error',
          error: `Unknown message type: ${type}`
        }));
    }
  }

  async handleChatMessage(ws, payload) {
    const { message, conversationId } = payload;
    
    try {
      // Send acknowledgment
      ws.send(JSON.stringify({
        type: 'message-received',
        conversationId
      }));

      // Get AI response with streaming
      const stream = await this.aiManager.getChatCompletion(message, {
        projectContext: this.fileSystemManager.getProjectInfo(),
        model: payload.model
      });

      let fullResponse = '';
      
      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          fullResponse += content;
          
          // Send streaming response
          ws.send(JSON.stringify({
            type: 'ai-response-chunk',
            conversationId,
            content,
            isComplete: false
          }));
        }
      }

      // Parse actions from the complete response
      const actions = this.actionParser.parseActions(fullResponse);
      
      // Send completion
      ws.send(JSON.stringify({
        type: 'ai-response-chunk',
        conversationId,
        content: '',
        isComplete: true,
        actions
      }));

      // Execute actions if any
      if (actions.length > 0) {
        await this.executeActions(ws, actions, conversationId);
      }

    } catch (error) {
      console.error('Chat message error:', error);
      ws.send(JSON.stringify({
        type: 'error',
        conversationId,
        error: error.message
      }));
    }
  }

  async executeActions(ws, actions, conversationId) {
    for (const action of actions) {
      try {
        if (!this.securityManager.isActionSafe(action)) {
          ws.send(JSON.stringify({
            type: 'action-error',
            conversationId,
            action,
            error: 'Action not permitted for security reasons'
          }));
          continue;
        }

        const result = await this.performAction(action);
        
        ws.send(JSON.stringify({
          type: 'action-result',
          conversationId,
          action,
          result
        }));

      } catch (error) {
        ws.send(JSON.stringify({
          type: 'action-error',
          conversationId,
          action,
          error: error.message
        }));
      }
    }
  }

  async performAction(action) {
    const { type, payload } = action;

    switch (type) {
      case 'CREATE_FILE':
        return this.fileSystemManager.writeFile(payload.path, payload.content);
      
      case 'UPDATE_FILE':
        return this.fileSystemManager.writeFile(payload.path, payload.content);
      
      case 'DELETE_FILE':
        return this.fileSystemManager.deleteFile(payload.path);
      
      case 'CREATE_DIRECTORY':
        return this.fileSystemManager.createDirectory(payload.path);
      
      case 'RUN_COMMAND':
        return this.fileSystemManager.runCommand(payload.command);
      
      default:
        throw new Error(`Unknown action type: ${type}`);
    }
  }

  setupFileWatcher() {
    // Watch for file changes in the project
    this.fileWatcher = chokidar.watch(this.projectPath, {
      ignored: /(^|[\/\\])\../, // ignore dotfiles
      persistent: true,
      ignoreInitial: true
    });

    this.fileWatcher.on('all', (event, filePath) => {
      // Broadcast file changes to all connected clients
      const relativePath = path.relative(this.projectPath, filePath);
      
      this.broadcast({
        type: 'file-change',
        event,
        path: relativePath
      });
    });
  }

  broadcast(message) {
    const messageStr = JSON.stringify(message);
    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(messageStr);
      }
    });
  }

  start() {
    this.server.listen(this.port, () => {
      console.log(`ChatProj server running on http://localhost:${this.port}`);
      console.log(`Project path: ${this.projectPath}`);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('Shutting down ChatProj server...');
      if (this.fileWatcher) {
        this.fileWatcher.close();
      }
      this.server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new ChatProjServer();
  server.start();
}

module.exports = ChatProjServer;
