
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for frmwrk-editor/lib</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> frmwrk-editor/lib</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">67.72% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>300/443</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">60.43% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>165/273</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">74.57% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>44/59</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">68.04% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>296/435</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="actionParser.js"><a href="actionParser.js.html">actionParser.js</a></td>
	<td data-value="76.05" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 76%"></div><div class="cover-empty" style="width: 24%"></div></div>
	</td>
	<td data-value="76.05" class="pct medium">76.05%</td>
	<td data-value="142" class="abs medium">108/142</td>
	<td data-value="59.61" class="pct medium">59.61%</td>
	<td data-value="104" class="abs medium">62/104</td>
	<td data-value="94.11" class="pct high">94.11%</td>
	<td data-value="17" class="abs high">16/17</td>
	<td data-value="75.53" class="pct medium">75.53%</td>
	<td data-value="139" class="abs medium">105/139</td>
	</tr>

<tr>
	<td class="file high" data-value="fileSystemManager.js"><a href="fileSystemManager.js.html">fileSystemManager.js</a></td>
	<td data-value="80.64" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 80%"></div><div class="cover-empty" style="width: 20%"></div></div>
	</td>
	<td data-value="80.64" class="pct high">80.64%</td>
	<td data-value="124" class="abs high">100/124</td>
	<td data-value="69.13" class="pct medium">69.13%</td>
	<td data-value="81" class="abs medium">56/81</td>
	<td data-value="93.75" class="pct high">93.75%</td>
	<td data-value="16" class="abs high">15/16</td>
	<td data-value="83.19" class="pct high">83.19%</td>
	<td data-value="119" class="abs high">99/119</td>
	</tr>

<tr>
	<td class="file low" data-value="openaiManager.js"><a href="openaiManager.js.html">openaiManager.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="62" class="abs low">0/62</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="26" class="abs low">0/26</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="62" class="abs low">0/62</td>
	</tr>

<tr>
	<td class="file high" data-value="securityManager.js"><a href="securityManager.js.html">securityManager.js</a></td>
	<td data-value="80" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 80%"></div><div class="cover-empty" style="width: 20%"></div></div>
	</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="115" class="abs high">92/115</td>
	<td data-value="75.8" class="pct medium">75.8%</td>
	<td data-value="62" class="abs medium">47/62</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="14" class="abs high">13/14</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="115" class="abs high">92/115</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-07T09:38:49.473Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    