<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatProj - AI Coding Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #1a1a1a;
            color: #e0e0e0;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: #2d2d2d;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .project-info {
            font-size: 14px;
            color: #888;
        }

        .file-tree {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 20px;
            background: #252525;
            border-radius: 8px;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
        }

        .message.user {
            background: #0066cc;
            margin-left: 20%;
        }

        .message.ai {
            background: #333;
            margin-right: 20%;
        }

        .message.system {
            background: #4CAF50;
            text-align: center;
            margin: 10px 0;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 1px solid #404040;
            border-radius: 8px;
            background: #2d2d2d;
            color: #e0e0e0;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 150px;
        }

        .send-button {
            padding: 15px 30px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.2s;
        }

        .send-button:hover {
            background: #45a049;
        }

        .send-button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #888;
        }

        .loading.show {
            display: block;
        }

        .status-bar {
            padding: 10px 20px;
            background: #1a1a1a;
            border-top: 1px solid #404040;
            font-size: 12px;
            color: #888;
        }

        .connection-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff4444;
            margin-right: 8px;
        }

        .connection-status.connected {
            background: #4CAF50;
        }

        pre {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }

        code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }

        .action-item {
            background: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            font-size: 12px;
        }

        .action-item.success {
            border-color: #4CAF50;
        }

        .action-item.error {
            border-color: #ff4444;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 250px;
            }
            
            .message.user {
                margin-left: 10%;
            }
            
            .message.ai {
                margin-right: 10%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">ChatProj</div>
                <div class="project-info" id="project-info">
                    Loading project...
                </div>
            </div>
            <div class="file-tree" id="file-tree">
                <div>Loading file tree...</div>
            </div>
        </div>

        <div class="main-content">
            <div class="chat-container">
                <div class="messages" id="messages">
                    <div class="message system">
                        Welcome to ChatProj! I'm your AI coding assistant. How can I help you with your project today?
                    </div>
                </div>
                
                <div class="loading" id="loading">
                    AI is thinking...
                </div>

                <div class="input-container">
                    <textarea 
                        id="message-input" 
                        class="message-input" 
                        placeholder="Ask me anything about your project..."
                        rows="1"
                    ></textarea>
                    <button id="send-button" class="send-button">Send</button>
                </div>
            </div>

            <div class="status-bar">
                <span class="connection-status" id="connection-status"></span>
                <span id="status-text">Connecting...</span>
            </div>
        </div>
    </div>

    <script>
        class ChatProjClient {
            constructor() {
                this.ws = null;
                this.connected = false;
                this.conversationId = Date.now().toString();
                
                this.initializeElements();
                this.setupEventListeners();
                this.connect();
            }

            initializeElements() {
                this.messagesContainer = document.getElementById('messages');
                this.messageInput = document.getElementById('message-input');
                this.sendButton = document.getElementById('send-button');
                this.loading = document.getElementById('loading');
                this.connectionStatus = document.getElementById('connection-status');
                this.statusText = document.getElementById('status-text');
                this.projectInfo = document.getElementById('project-info');
                this.fileTree = document.getElementById('file-tree');
            }

            setupEventListeners() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // Auto-resize textarea
                this.messageInput.addEventListener('input', () => {
                    this.messageInput.style.height = 'auto';
                    this.messageInput.style.height = this.messageInput.scrollHeight + 'px';
                });
            }

            connect() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}`;
                
                this.ws = new WebSocket(wsUrl);

                this.ws.onopen = () => {
                    this.connected = true;
                    this.updateConnectionStatus();
                    console.log('Connected to ChatProj server');
                };

                this.ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                };

                this.ws.onclose = () => {
                    this.connected = false;
                    this.updateConnectionStatus();
                    console.log('Disconnected from ChatProj server');
                    
                    // Attempt to reconnect after 3 seconds
                    setTimeout(() => this.connect(), 3000);
                };

                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                };
            }

            updateConnectionStatus() {
                if (this.connected) {
                    this.connectionStatus.classList.add('connected');
                    this.statusText.textContent = 'Connected';
                } else {
                    this.connectionStatus.classList.remove('connected');
                    this.statusText.textContent = 'Disconnected';
                }
            }

            handleMessage(data) {
                switch (data.type) {
                    case 'project-info':
                        this.updateProjectInfo(data.data);
                        break;
                    case 'file-tree':
                        this.updateFileTree(data.data);
                        break;
                    case 'ai-response-chunk':
                        this.handleAIResponse(data);
                        break;
                    case 'action-result':
                        this.handleActionResult(data);
                        break;
                    case 'error':
                        this.addMessage('system', `Error: ${data.error}`);
                        break;
                }
            }

            updateProjectInfo(info) {
                this.projectInfo.innerHTML = `
                    <strong>${info.name}</strong><br>
                    <small>${info.type} project</small>
                `;
            }

            updateFileTree(tree) {
                this.fileTree.innerHTML = this.renderFileTree(tree);
            }

            renderFileTree(node, depth = 0) {
                if (!node) return '';
                
                const indent = '  '.repeat(depth);
                const icon = node.type === 'directory' ? '📁' : '📄';
                
                let html = `<div style="margin-left: ${depth * 15}px">${icon} ${node.name}</div>`;
                
                if (node.children) {
                    for (const child of node.children) {
                        html += this.renderFileTree(child, depth + 1);
                    }
                }
                
                return html;
            }

            sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || !this.connected) return;

                this.addMessage('user', message);
                this.messageInput.value = '';
                this.messageInput.style.height = 'auto';
                
                this.showLoading(true);
                this.sendButton.disabled = true;

                this.ws.send(JSON.stringify({
                    type: 'chat-message',
                    payload: {
                        message,
                        conversationId: this.conversationId
                    }
                }));
            }

            handleAIResponse(data) {
                if (data.isComplete) {
                    this.showLoading(false);
                    this.sendButton.disabled = false;
                } else {
                    this.appendToLastMessage(data.content);
                }
            }

            handleActionResult(data) {
                const actionText = `${data.action.type}: ${data.action.payload.path || data.action.payload.command}`;
                this.addMessage('system', `✅ ${actionText}`);
            }

            addMessage(type, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.innerHTML = this.formatMessage(content);
                
                this.messagesContainer.appendChild(messageDiv);
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                
                if (type === 'ai') {
                    this.currentAIMessage = messageDiv;
                }
            }

            appendToLastMessage(content) {
                if (this.currentAIMessage) {
                    this.currentAIMessage.innerHTML += content;
                } else {
                    this.addMessage('ai', content);
                }
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }

            formatMessage(content) {
                // Basic markdown-like formatting
                return content
                    .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code>$2</code></pre>')
                    .replace(/`([^`]+)`/g, '<code>$1</code>')
                    .replace(/\n/g, '<br>');
            }

            showLoading(show) {
                if (show) {
                    this.loading.classList.add('show');
                } else {
                    this.loading.classList.remove('show');
                }
            }
        }

        // Initialize the client when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ChatProjClient();
        });
    </script>
</body>
</html>
