import React from 'react';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  FileText, 
  FolderPlus, 
  Trash2, 
  Terminal,
  Package
} from 'lucide-react';

const ActionStatus = ({ action }) => {
  const { type, payload, status, error, result, timestamp } = action;

  const getActionIcon = () => {
    switch (type) {
      case 'CREATE_FILE':
      case 'UPDATE_FILE':
        return <FileText className="w-4 h-4" />;
      case 'DELETE_FILE':
        return <Trash2 className="w-4 h-4" />;
      case 'CREATE_DIRECTORY':
        return <FolderPlus className="w-4 h-4" />;
      case 'RUN_COMMAND':
        return <Terminal className="w-4 h-4" />;
      case 'INSTALL_PACKAGE':
        return <Package className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'pending':
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'border-green-500 bg-green-900/20';
      case 'error':
        return 'border-red-500 bg-red-900/20';
      case 'pending':
      default:
        return 'border-yellow-500 bg-yellow-900/20';
    }
  };

  const getActionDescription = () => {
    switch (type) {
      case 'CREATE_FILE':
        return `Create file: ${payload.path}`;
      case 'UPDATE_FILE':
        return `Update file: ${payload.path}`;
      case 'DELETE_FILE':
        return `Delete file: ${payload.path}`;
      case 'CREATE_DIRECTORY':
        return `Create directory: ${payload.path}`;
      case 'RUN_COMMAND':
        return `Run command: ${payload.command}`;
      case 'INSTALL_PACKAGE':
        return `Install package: ${payload.package}`;
      default:
        return `${type}: ${JSON.stringify(payload)}`;
    }
  };

  const formatTimestamp = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  };

  return (
    <div className={`border rounded-lg p-3 ${getStatusColor()}`}>
      <div className="flex items-start gap-3">
        {/* Action icon */}
        <div className="flex-shrink-0 mt-0.5">
          {getActionIcon()}
        </div>

        {/* Action details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium text-gray-200">
              {getActionDescription()}
            </span>
            {getStatusIcon()}
          </div>

          {/* Timestamp */}
          <div className="text-xs text-gray-400">
            {formatTimestamp(timestamp)}
          </div>

          {/* Error message */}
          {status === 'error' && error && (
            <div className="mt-2 text-sm text-red-300 bg-red-900/30 rounded p-2">
              <strong>Error:</strong> {error}
            </div>
          )}

          {/* Success result */}
          {status === 'success' && result && (
            <div className="mt-2">
              {result.stdout && (
                <div className="text-sm text-green-300 bg-green-900/30 rounded p-2">
                  <strong>Output:</strong>
                  <pre className="mt-1 text-xs whitespace-pre-wrap">
                    {result.stdout}
                  </pre>
                </div>
              )}
              
              {result.stderr && (
                <div className="text-sm text-yellow-300 bg-yellow-900/30 rounded p-2 mt-1">
                  <strong>Warnings:</strong>
                  <pre className="mt-1 text-xs whitespace-pre-wrap">
                    {result.stderr}
                  </pre>
                </div>
              )}
            </div>
          )}

          {/* Pending indicator */}
          {status === 'pending' && (
            <div className="mt-2 flex items-center gap-2 text-sm text-yellow-300">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
              Executing...
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActionStatus;
