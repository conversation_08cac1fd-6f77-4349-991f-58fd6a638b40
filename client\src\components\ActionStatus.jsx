import * as React from 'react';
import {
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  FolderPlus,
  Trash2,
  Terminal,
  Package
} from 'lucide-react';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';

const ActionStatus = ({ action }) => {
  const { type, payload, status, error, result, timestamp } = action;

  const getActionIcon = () => {
    switch (type) {
      case 'CREATE_FILE':
      case 'UPDATE_FILE':
        return <FileText className="w-4 h-4" />;
      case 'DELETE_FILE':
        return <Trash2 className="w-4 h-4" />;
      case 'CREATE_DIRECTORY':
        return <FolderPlus className="w-4 h-4" />;
      case 'RUN_COMMAND':
        return <Terminal className="w-4 h-4" />;
      case 'INSTALL_PACKAGE':
        return <Package className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'pending':
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'border-green-500 bg-green-900/20';
      case 'error':
        return 'border-red-500 bg-red-900/20';
      case 'pending':
      default:
        return 'border-yellow-500 bg-yellow-900/20';
    }
  };

  const getActionDescription = () => {
    switch (type) {
      case 'CREATE_FILE':
        return `Create file: ${payload.path}`;
      case 'UPDATE_FILE':
        return `Update file: ${payload.path}`;
      case 'DELETE_FILE':
        return `Delete file: ${payload.path}`;
      case 'CREATE_DIRECTORY':
        return `Create directory: ${payload.path}`;
      case 'RUN_COMMAND':
        return `Run command: ${payload.command}`;
      case 'INSTALL_PACKAGE':
        return `Install package: ${payload.package}`;
      default:
        return `${type}: ${JSON.stringify(payload)}`;
    }
  };

  const formatTimestamp = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  };

  return (
    <Card className={`${getStatusColor()}`}>
      <CardContent className="p-3">
        <div className="flex items-start gap-3">
          {/* Action icon */}
          <div className="flex-shrink-0 mt-0.5">
            {getActionIcon()}
          </div>

          {/* Action details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-sm font-medium">
                {getActionDescription()}
              </span>
              {getStatusIcon()}
            </div>

            {/* Timestamp */}
            <div className="text-xs text-muted-foreground">
              {formatTimestamp(timestamp)}
            </div>

            {/* Error message */}
            {status === 'error' && error && (
              <div className="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded">
                <div className="text-sm text-destructive">
                  <strong>Error:</strong> {error}
                </div>
              </div>
            )}

            {/* Success result */}
            {status === 'success' && result && (
              <div className="mt-2 space-y-2">
                {result.stdout && (
                  <div className="p-2 bg-green-500/10 border border-green-500/20 rounded">
                    <div className="text-sm text-green-700 dark:text-green-300">
                      <strong>Output:</strong>
                    </div>
                    <pre className="mt-1 text-xs whitespace-pre-wrap font-mono">
                      {result.stdout}
                    </pre>
                  </div>
                )}

                {result.stderr && (
                  <div className="p-2 bg-yellow-500/10 border border-yellow-500/20 rounded">
                    <div className="text-sm text-yellow-700 dark:text-yellow-300">
                      <strong>Warnings:</strong>
                    </div>
                    <pre className="mt-1 text-xs whitespace-pre-wrap font-mono">
                      {result.stderr}
                    </pre>
                  </div>
                )}
              </div>
            )}

            {/* Pending indicator */}
            {status === 'pending' && (
              <div className="mt-2 flex items-center gap-2">
                <Badge variant="secondary" className="animate-pulse">
                  <div className="w-2 h-2 bg-current rounded-full mr-1" />
                  Executing...
                </Badge>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ActionStatus;
