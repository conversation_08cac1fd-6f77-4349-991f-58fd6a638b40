const ActionParser = require('../lib/actionParser');

describe('ActionParser', () => {
  let parser;

  beforeEach(() => {
    parser = new ActionParser();
  });

  describe('parseActions', () => {
    test('should parse JSON actions from code blocks', () => {
      const response = `
I'll create a new file for you.

\`\`\`json
{
  "actions": [
    {
      "type": "CREATE_FILE",
      "payload": {
        "path": "src/components/Button.jsx",
        "content": "import React from 'react';\\n\\nconst Button = () => {\\n  return <button>Click me</button>;\\n};\\n\\nexport default Button;"
      }
    }
  ]
}
\`\`\`

The file has been created successfully.
      `;

      const actions = parser.parseActions(response);
      
      expect(actions).toHaveLength(1);
      expect(actions[0]).toMatchObject({
        type: 'CREATE_FILE',
        payload: {
          path: 'src/components/Button.jsx',
          content: expect.stringContaining('import React')
        }
      });
    });

    test('should parse text-based actions', () => {
      const response = `
I'll create a new component for you.

CREATE FILE: src/Button.js
\`\`\`javascript
import React from 'react';

const Button = ({ children, onClick }) => {
  return (
    <button onClick={onClick}>
      {children}
    </button>
  );
};

export default Button;
\`\`\`

RUN COMMAND: npm install react-router-dom
      `;

      const actions = parser.parseActions(response);
      
      expect(actions).toHaveLength(2);
      expect(actions[0]).toMatchObject({
        type: 'CREATE_FILE',
        payload: {
          path: 'src/Button.js',
          content: expect.stringContaining('import React')
        }
      });
      expect(actions[1]).toMatchObject({
        type: 'RUN_COMMAND',
        payload: {
          command: 'npm install react-router-dom'
        }
      });
    });

    test('should handle npm install patterns', () => {
      const response = 'You need to npm install express cors helmet';
      
      const actions = parser.parseActions(response);
      
      expect(actions).toHaveLength(1);
      expect(actions[0]).toMatchObject({
        type: 'RUN_COMMAND',
        payload: {
          command: 'npm install express cors helmet'
        }
      });
    });

    test('should return empty array for response without actions', () => {
      const response = 'This is just a regular response without any actions.';
      
      const actions = parser.parseActions(response);
      
      expect(actions).toHaveLength(0);
    });
  });

  describe('validateAction', () => {
    test('should validate correct CREATE_FILE action', () => {
      const action = {
        type: 'CREATE_FILE',
        payload: {
          path: 'test.js',
          content: 'console.log("hello");'
        }
      };

      expect(parser.validateAction(action)).toBe(true);
    });

    test('should validate correct RUN_COMMAND action', () => {
      const action = {
        type: 'RUN_COMMAND',
        payload: {
          command: 'npm install express'
        }
      };

      expect(parser.validateAction(action)).toBe(true);
    });

    test('should reject action with invalid type', () => {
      const action = {
        type: 'INVALID_ACTION',
        payload: {
          path: 'test.js'
        }
      };

      expect(parser.validateAction(action)).toBe(false);
    });

    test('should reject action without payload', () => {
      const action = {
        type: 'CREATE_FILE'
      };

      expect(parser.validateAction(action)).toBe(false);
    });

    test('should reject CREATE_FILE action without content', () => {
      const action = {
        type: 'CREATE_FILE',
        payload: {
          path: 'test.js'
        }
      };

      expect(parser.validateAction(action)).toBe(false);
    });
  });

  describe('normalizeAction', () => {
    test('should normalize file paths', () => {
      const action = {
        type: 'CREATE_FILE',
        payload: {
          path: '"src/components/Button.jsx"',
          content: 'content'
        }
      };

      const normalized = parser.normalizeAction(action);
      
      expect(normalized.payload.path).toBe('src/components/Button.jsx');
    });

    test('should normalize content with escaped characters', () => {
      const action = {
        type: 'CREATE_FILE',
        payload: {
          path: 'test.js',
          content: 'console.log(\\"hello\\");\\nconst x = 1;'
        }
      };

      const normalized = parser.normalizeAction(action);
      
      expect(normalized.payload.content).toBe('console.log("hello");\nconst x = 1;');
    });

    test('should normalize commands', () => {
      const action = {
        type: 'RUN_COMMAND',
        payload: {
          command: '  npm install express  '
        }
      };

      const normalized = parser.normalizeAction(action);
      
      expect(normalized.payload.command).toBe('npm install express');
    });
  });

  describe('extractCodeBlock', () => {
    test('should extract code block from lines', () => {
      const lines = [
        'Here is some code:',
        '```javascript',
        'const x = 1;',
        'console.log(x);',
        '```',
        'End of code.'
      ];

      const content = parser.extractCodeBlock(lines, 1);
      
      expect(content).toBe('const x = 1;\nconsole.log(x);');
    });

    test('should return empty string if no code block found', () => {
      const lines = [
        'No code here',
        'Just text'
      ];

      const content = parser.extractCodeBlock(lines, 0);
      
      expect(content).toBe('');
    });
  });

  describe('parseActionsWithConfidence', () => {
    test('should assign confidence scores to actions', () => {
      const response = `
\`\`\`json
{
  "actions": [
    {
      "type": "CREATE_FILE",
      "payload": {
        "path": "test.js",
        "content": "content"
      }
    }
  ]
}
\`\`\`
      `;

      const actions = parser.parseActionsWithConfidence(response);
      
      expect(actions).toHaveLength(1);
      expect(actions[0]).toHaveProperty('confidence');
      expect(actions[0].confidence).toBeGreaterThan(0.5);
    });
  });

  describe('groupActions', () => {
    test('should group related file operations', () => {
      const actions = [
        {
          type: 'CREATE_FILE',
          payload: { path: 'test.js', content: 'content1' }
        },
        {
          type: 'UPDATE_FILE',
          payload: { path: 'test.js', content: 'content2' }
        },
        {
          type: 'CREATE_FILE',
          payload: { path: 'other.js', content: 'content3' }
        }
      ];

      const groups = parser.groupActions(actions);
      
      expect(groups).toHaveLength(2);
      expect(groups[0]).toHaveLength(2); // test.js operations
      expect(groups[1]).toHaveLength(1); // other.js operation
    });
  });
});
