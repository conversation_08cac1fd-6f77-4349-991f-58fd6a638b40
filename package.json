{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Universal AI coding assistant that launches as a web interface from any project directory", "main": "server.js", "bin": {"chatproj": "./cli.js"}, "scripts": {"start": "node server.js", "dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "nodemon server.js", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["ai", "coding", "assistant", "cli", "development", "openai", "chatgpt", "code-generation"], "author": "ChatProj Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "openai": "^4.20.1", "commander": "^11.1.0", "chalk": "^4.1.2", "ora": "^5.4.1", "open": "^8.4.2", "chokidar": "^3.5.3", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "mime-types": "^2.1.35", "sanitize-filename": "^1.6.3"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.2", "jest": "^29.7.0", "eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.3.1", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/chatproj/chatproj.git"}, "bugs": {"url": "https://github.com/chatproj/chatproj/issues"}, "homepage": "https://github.com/chatproj/chatproj#readme"}