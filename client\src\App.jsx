import React, { useState, useEffect, useRef } from 'react';
import { Send, FileText, Folder, FolderOpen, Code, Terminal, Zap } from 'lucide-react';
import ChatMessage from './components/ChatMessage';
import FileTree from './components/FileTree';
import ActionStatus from './components/ActionStatus';
import { useWebSocket } from './hooks/useWebSocket';
import { formatMessage } from './utils/messageFormatter';

function App() {
  const [messages, setMessages] = useState([
    {
      id: 'welcome',
      type: 'system',
      content: 'Welcome to ChatProj! I\'m your AI coding assistant. How can I help you with your project today?',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [projectInfo, setProjectInfo] = useState(null);
  const [fileTree, setFileTree] = useState(null);
  const [actions, setActions] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const conversationId = useRef(Date.now().toString());

  const { 
    isConnected, 
    sendMessage: sendWebSocketMessage, 
    lastMessage 
  } = useWebSocket();

  // Handle WebSocket messages
  useEffect(() => {
    if (!lastMessage) return;

    const data = lastMessage;
    
    switch (data.type) {
      case 'project-info':
        setProjectInfo(data.data);
        break;
        
      case 'file-tree':
        setFileTree(data.data);
        break;
        
      case 'ai-response-chunk':
        handleAIResponse(data);
        break;
        
      case 'action-result':
        handleActionResult(data);
        break;
        
      case 'action-error':
        handleActionError(data);
        break;
        
      case 'error':
        addSystemMessage(`Error: ${data.error}`);
        setIsLoading(false);
        break;
    }
  }, [lastMessage]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleAIResponse = (data) => {
    if (data.isComplete) {
      setIsLoading(false);
      if (data.actions && data.actions.length > 0) {
        setActions(prev => [...prev, ...data.actions.map(action => ({
          ...action,
          id: Date.now() + Math.random(),
          status: 'pending',
          timestamp: new Date()
        }))]);
      }
    } else if (data.content) {
      // Stream the AI response
      setMessages(prev => {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage && lastMessage.type === 'ai' && lastMessage.streaming) {
          return [
            ...prev.slice(0, -1),
            {
              ...lastMessage,
              content: lastMessage.content + data.content
            }
          ];
        } else {
          return [
            ...prev,
            {
              id: data.conversationId + '-ai',
              type: 'ai',
              content: data.content,
              streaming: true,
              timestamp: new Date()
            }
          ];
        }
      });
    }
  };

  const handleActionResult = (data) => {
    setActions(prev => prev.map(action => 
      action.id === data.action.id 
        ? { ...action, status: 'success', result: data.result }
        : action
    ));
    
    addSystemMessage(`✅ ${data.action.type}: ${data.action.payload.path || data.action.payload.command}`);
  };

  const handleActionError = (data) => {
    setActions(prev => prev.map(action => 
      action.id === data.action.id 
        ? { ...action, status: 'error', error: data.error }
        : action
    ));
    
    addSystemMessage(`❌ ${data.action.type} failed: ${data.error}`);
  };

  const addSystemMessage = (content) => {
    setMessages(prev => [...prev, {
      id: Date.now().toString(),
      type: 'system',
      content,
      timestamp: new Date()
    }]);
  };

  const sendMessage = () => {
    const message = inputMessage.trim();
    if (!message || !isConnected || isLoading) return;

    // Add user message
    setMessages(prev => [...prev, {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    }]);

    // Clear input and set loading
    setInputMessage('');
    setIsLoading(true);

    // Send to server
    sendWebSocketMessage({
      type: 'chat-message',
      payload: {
        message,
        conversationId: conversationId.current
      }
    });
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleFileSelect = (file) => {
    setSelectedFile(file);
    // Could load file content here if needed
  };

  return (
    <div className="flex h-screen bg-gray-900 text-gray-100">
      {/* Sidebar */}
      <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center gap-2 mb-3">
            <Zap className="w-6 h-6 text-green-500" />
            <h1 className="text-xl font-bold text-green-500">ChatProj</h1>
          </div>
          
          {projectInfo && (
            <div className="text-sm text-gray-400">
              <div className="font-medium text-gray-200">{projectInfo.name}</div>
              <div className="capitalize">{projectInfo.type} project</div>
              {projectInfo.version && (
                <div>v{projectInfo.version}</div>
              )}
            </div>
          )}
        </div>

        {/* File Tree */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Project Files
            </h3>
            {fileTree ? (
              <FileTree 
                tree={fileTree} 
                onFileSelect={handleFileSelect}
                selectedFile={selectedFile}
              />
            ) : (
              <div className="text-sm text-gray-500">Loading...</div>
            )}
          </div>
        </div>

        {/* Connection Status */}
        <div className="p-4 border-t border-gray-700">
          <div className="flex items-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-gray-400">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-4xl mx-auto space-y-4">
            {messages.map((message) => (
              <ChatMessage 
                key={message.id} 
                message={message} 
              />
            ))}
            
            {isLoading && (
              <div className="flex justify-center py-4">
                <div className="flex items-center gap-2 text-gray-400">
                  <div className="loading-pulse">AI is thinking...</div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Action Status */}
        {actions.length > 0 && (
          <div className="border-t border-gray-700 p-4">
            <div className="max-w-4xl mx-auto">
              <h3 className="text-sm font-medium text-gray-300 mb-2 flex items-center gap-2">
                <Terminal className="w-4 h-4" />
                Recent Actions
              </h3>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {actions.slice(-5).map((action) => (
                  <ActionStatus key={action.id} action={action} />
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Input */}
        <div className="border-t border-gray-700 p-6">
          <div className="max-w-4xl mx-auto">
            <div className="flex gap-4">
              <textarea
                ref={inputRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me anything about your project..."
                className="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                rows="1"
                style={{
                  minHeight: '50px',
                  maxHeight: '150px',
                  height: 'auto'
                }}
                onInput={(e) => {
                  e.target.style.height = 'auto';
                  e.target.style.height = e.target.scrollHeight + 'px';
                }}
                disabled={!isConnected || isLoading}
              />
              <button
                onClick={sendMessage}
                disabled={!inputMessage.trim() || !isConnected || isLoading}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg transition-colors flex items-center gap-2"
              >
                <Send className="w-4 h-4" />
                Send
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
