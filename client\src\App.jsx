import React, { useState, useEffect, useRef } from 'react';
import { Send, FileText, Folder, FolderOpen, Code, Terminal, Zap, Settings, Bot } from 'lucide-react';
import ChatMessage from './components/ChatMessage';
import FileTree from './components/FileTree';
import ActionStatus from './components/ActionStatus';
import ModelSelector from './components/ModelSelector';
import { useWebSocket } from './hooks/useWebSocket';
import { formatMessage } from './utils/messageFormatter';
import { Button } from './components/ui/button';
import { Textarea } from './components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from './components/ui/card';
import { ScrollArea } from './components/ui/scroll-area';
import { Separator } from './components/ui/separator';
import { Badge } from './components/ui/badge';
import { Toaster } from './components/ui/toaster';
import { useToast } from './components/ui/use-toast';

function App() {
  const [messages, setMessages] = useState([
    {
      id: 'welcome',
      type: 'system',
      content: 'Welcome to ChatProj! I\'m your AI coding assistant. How can I help you with your project today?',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [projectInfo, setProjectInfo] = useState(null);
  const [fileTree, setFileTree] = useState(null);
  const [actions, setActions] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [aiProviders, setAiProviders] = useState(null);
  const [selectedModel, setSelectedModel] = useState(null);
  const [showSettings, setShowSettings] = useState(false);
  
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const conversationId = useRef(Date.now().toString());

  const {
    isConnected,
    sendMessage: sendWebSocketMessage,
    lastMessage
  } = useWebSocket();

  const { toast } = useToast();

  // Fetch AI providers on mount
  useEffect(() => {
    if (isConnected) {
      fetch('/api/ai-providers')
        .then(res => res.json())
        .then(data => {
          setAiProviders(data);
          setSelectedModel(data.defaultModel);
        })
        .catch(err => {
          console.error('Failed to fetch AI providers:', err);
          toast({
            title: "Error",
            description: "Failed to load AI provider information",
            variant: "destructive"
          });
        });
    }
  }, [isConnected, toast]);

  // Handle WebSocket messages
  useEffect(() => {
    if (!lastMessage) return;

    const data = lastMessage;
    
    switch (data.type) {
      case 'project-info':
        setProjectInfo(data.data);
        break;
        
      case 'file-tree':
        setFileTree(data.data);
        break;
        
      case 'ai-response-chunk':
        handleAIResponse(data);
        break;
        
      case 'action-result':
        handleActionResult(data);
        break;
        
      case 'action-error':
        handleActionError(data);
        break;
        
      case 'error':
        addSystemMessage(`Error: ${data.error}`);
        setIsLoading(false);
        break;
    }
  }, [lastMessage]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleAIResponse = (data) => {
    if (data.isComplete) {
      setIsLoading(false);
      if (data.actions && data.actions.length > 0) {
        setActions(prev => [...prev, ...data.actions.map(action => ({
          ...action,
          id: Date.now() + Math.random(),
          status: 'pending',
          timestamp: new Date()
        }))]);
      }
    } else if (data.content) {
      // Stream the AI response
      setMessages(prev => {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage && lastMessage.type === 'ai' && lastMessage.streaming) {
          return [
            ...prev.slice(0, -1),
            {
              ...lastMessage,
              content: lastMessage.content + data.content
            }
          ];
        } else {
          return [
            ...prev,
            {
              id: data.conversationId + '-ai',
              type: 'ai',
              content: data.content,
              streaming: true,
              timestamp: new Date()
            }
          ];
        }
      });
    }
  };

  const handleActionResult = (data) => {
    setActions(prev => prev.map(action => 
      action.id === data.action.id 
        ? { ...action, status: 'success', result: data.result }
        : action
    ));
    
    addSystemMessage(`✅ ${data.action.type}: ${data.action.payload.path || data.action.payload.command}`);
  };

  const handleActionError = (data) => {
    setActions(prev => prev.map(action => 
      action.id === data.action.id 
        ? { ...action, status: 'error', error: data.error }
        : action
    ));
    
    addSystemMessage(`❌ ${data.action.type} failed: ${data.error}`);
  };

  const addSystemMessage = (content) => {
    setMessages(prev => [...prev, {
      id: Date.now().toString(),
      type: 'system',
      content,
      timestamp: new Date()
    }]);
  };

  const sendMessage = () => {
    const message = inputMessage.trim();
    if (!message || !isConnected || isLoading) return;

    // Add user message
    setMessages(prev => [...prev, {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    }]);

    // Clear input and set loading
    setInputMessage('');
    setIsLoading(true);

    // Send to server
    sendWebSocketMessage({
      type: 'chat-message',
      payload: {
        message,
        conversationId: conversationId.current,
        model: selectedModel
      }
    });
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleFileSelect = (file) => {
    setSelectedFile(file);
    // Could load file content here if needed
  };

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Sidebar */}
      <div className="w-80 border-r bg-card flex flex-col">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className="p-2 bg-primary rounded-lg">
                <Zap className="w-5 h-5 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-bold">ChatProj</h1>
                <p className="text-xs text-muted-foreground">AI Coding Assistant</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>

          {projectInfo && (
            <Card className="mb-4">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Code className="w-4 h-4 text-muted-foreground" />
                  <span className="font-medium">{projectInfo.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    {projectInfo.type}
                  </Badge>
                  {projectInfo.version && (
                    <Badge variant="outline" className="text-xs">
                      v{projectInfo.version}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Model Selector */}
          {showSettings && aiProviders && (
            <Card className="mb-4">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Bot className="w-4 h-4" />
                  AI Model
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <ModelSelector
                  providers={aiProviders}
                  selectedModel={selectedModel}
                  onModelChange={setSelectedModel}
                />
              </CardContent>
            </Card>
          )}
        </div>

        {/* File Tree */}
        <div className="flex-1 overflow-hidden">
          <div className="p-4 h-full flex flex-col">
            <div className="flex items-center gap-2 mb-3">
              <FileText className="w-4 h-4 text-muted-foreground" />
              <h3 className="text-sm font-medium">Project Files</h3>
            </div>
            <ScrollArea className="flex-1">
              {fileTree ? (
                <FileTree
                  tree={fileTree}
                  onFileSelect={handleFileSelect}
                  selectedFile={selectedFile}
                />
              ) : (
                <div className="text-sm text-muted-foreground">Loading...</div>
              )}
            </ScrollArea>
          </div>
        </div>

        {/* Connection Status */}
        <div className="p-4 border-t">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-destructive'}`} />
              <span className="text-muted-foreground">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            {aiProviders && (
              <Badge variant="outline" className="text-xs">
                {aiProviders.provider}
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-background">
        {/* Header */}
        <div className="border-b p-4">
          <div className="max-w-4xl mx-auto flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Bot className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="font-semibold">AI Assistant</h2>
                <p className="text-sm text-muted-foreground">
                  {selectedModel ? `Using ${selectedModel}` : 'Ready to help'}
                </p>
              </div>
            </div>
            {isLoading && (
              <Badge variant="secondary" className="animate-pulse">
                Thinking...
              </Badge>
            )}
          </div>
        </div>

        {/* Messages */}
        <ScrollArea className="flex-1 p-6">
          <div className="max-w-4xl mx-auto space-y-6">
            {messages.map((message) => (
              <ChatMessage
                key={message.id}
                message={message}
              />
            ))}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Action Status */}
        {actions.length > 0 && (
          <div className="border-t p-4 bg-muted/30">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center gap-2 mb-3">
                <Terminal className="w-4 h-4 text-muted-foreground" />
                <h3 className="text-sm font-medium">Recent Actions</h3>
                <Badge variant="secondary" className="text-xs">
                  {actions.length}
                </Badge>
              </div>
              <ScrollArea className="max-h-32">
                <div className="space-y-2">
                  {actions.slice(-5).map((action) => (
                    <ActionStatus key={action.id} action={action} />
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        )}

        {/* Input */}
        <div className="border-t p-6 bg-background">
          <div className="max-w-4xl mx-auto">
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <Textarea
                  ref={inputRef}
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyDown={handleKeyPress}
                  placeholder="Ask me anything about your project..."
                  className="min-h-[60px] max-h-[150px] resize-none pr-12"
                  disabled={!isConnected || isLoading}
                />
                {inputMessage && (
                  <div className="absolute bottom-3 right-3 text-xs text-muted-foreground">
                    {inputMessage.length}/2000
                  </div>
                )}
              </div>
              <Button
                onClick={sendMessage}
                disabled={!inputMessage.trim() || !isConnected || isLoading}
                size="lg"
                className="px-6"
              >
                <Send className="w-4 h-4 mr-2" />
                Send
              </Button>
            </div>

            {/* Quick Actions */}
            <div className="flex gap-2 mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setInputMessage("What files are in this project?")}
                disabled={isLoading}
              >
                📁 Explore Files
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setInputMessage("Create a simple React component")}
                disabled={isLoading}
              >
                ⚛️ Create Component
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setInputMessage("Help me debug this code")}
                disabled={isLoading}
              >
                🐛 Debug Code
              </Button>
            </div>
          </div>
        </div>
      </div>

      <Toaster />
    </div>
  );
}

export default App;
