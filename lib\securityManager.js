const path = require('path');
const fs = require('fs');

class SecurityManager {
  constructor(projectPath) {
    this.projectPath = path.resolve(projectPath);
    
    // Dangerous file patterns to block
    this.blockedPatterns = [
      /\.env$/i,
      /\.env\./i,
      /password/i,
      /secret/i,
      /private.*key/i,
      /\.pem$/i,
      /\.key$/i,
      /\.crt$/i,
      /\.p12$/i,
      /\.pfx$/i,
      /config.*prod/i,
      /\.aws/i,
      /\.ssh/i
    ];

    // Dangerous directories to block
    this.blockedDirectories = [
      '.git',
      '.ssh',
      '.aws',
      '.docker',
      'node_modules',
      '.env',
      'secrets',
      'private',
      '.chatproj'
    ];

    // Safe commands whitelist
    this.safeCommands = [
      'npm',
      'yarn',
      'pnpm',
      'node',
      'git',
      'python',
      'python3',
      'pip',
      'pip3',
      'cargo',
      'go',
      'rustc',
      'tsc',
      'eslint',
      'prettier',
      'jest',
      'mocha',
      'webpack',
      'vite',
      'rollup'
    ];

    // Dangerous command patterns
    this.dangerousCommandPatterns = [
      /rm\s+-rf/i,
      /sudo/i,
      /chmod\s+777/i,
      /curl.*\|\s*sh/i,
      /wget.*\|\s*sh/i,
      /eval/i,
      /exec/i,
      /system/i,
      />/i, // Redirection
      /</i, // Input redirection
      /\|/i, // Pipes (except for safe ones)
      /;/i,  // Command chaining
      /&&/i, // Command chaining
      /\|\|/i // Command chaining
    ];

    // File size limits (in bytes)
    this.maxFileSize = 5 * 1024 * 1024; // 5MB
    this.maxTotalSize = 50 * 1024 * 1024; // 50MB total

    // Rate limiting
    this.actionCounts = new Map();
    this.maxActionsPerMinute = 30;
  }

  // Check if a file path is safe to access
  isPathSafe(relativePath) {
    try {
      // Normalize the path
      const normalizedPath = path.normalize(relativePath);
      const fullPath = path.resolve(this.projectPath, normalizedPath);
      
      // Ensure path is within project directory
      if (!fullPath.startsWith(this.projectPath)) {
        console.warn(`Path outside project directory: ${relativePath}`);
        return false;
      }

      // Check for blocked patterns
      for (const pattern of this.blockedPatterns) {
        if (pattern.test(normalizedPath)) {
          console.warn(`Blocked file pattern: ${relativePath}`);
          return false;
        }
      }

      // Check for blocked directories
      const pathParts = normalizedPath.split(path.sep);
      for (const part of pathParts) {
        if (this.blockedDirectories.includes(part.toLowerCase())) {
          console.warn(`Blocked directory in path: ${relativePath}`);
          return false;
        }
      }

      // Check for hidden files (except allowed ones)
      const fileName = path.basename(normalizedPath);
      if (fileName.startsWith('.') && !this.isAllowedHiddenFile(fileName)) {
        console.warn(`Blocked hidden file: ${relativePath}`);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`Path validation error: ${error.message}`);
      return false;
    }
  }

  // Check if a hidden file is allowed
  isAllowedHiddenFile(fileName) {
    const allowedHiddenFiles = [
      '.gitignore',
      '.eslintrc.js',
      '.eslintrc.json',
      '.prettierrc',
      '.babelrc',
      '.editorconfig',
      '.nvmrc',
      '.node-version'
    ];
    
    return allowedHiddenFiles.includes(fileName.toLowerCase());
  }

  // Check if a command is safe to execute
  isCommandSafe(command) {
    const trimmedCommand = command.trim();
    
    if (!trimmedCommand) {
      return false;
    }

    // Extract base command
    const baseCommand = trimmedCommand.split(' ')[0].toLowerCase();
    
    // Check if base command is in whitelist
    if (!this.safeCommands.includes(baseCommand)) {
      console.warn(`Command not in whitelist: ${baseCommand}`);
      return false;
    }

    // Check for dangerous patterns
    for (const pattern of this.dangerousCommandPatterns) {
      if (pattern.test(trimmedCommand)) {
        console.warn(`Dangerous command pattern detected: ${trimmedCommand}`);
        return false;
      }
    }

    // Additional checks for specific commands
    if (baseCommand === 'npm' || baseCommand === 'yarn' || baseCommand === 'pnpm') {
      return this.isPackageManagerCommandSafe(trimmedCommand);
    }

    if (baseCommand === 'git') {
      return this.isGitCommandSafe(trimmedCommand);
    }

    return true;
  }

  // Check if package manager command is safe
  isPackageManagerCommandSafe(command) {
    const safeNpmCommands = [
      'install',
      'i',
      'add',
      'remove',
      'uninstall',
      'update',
      'outdated',
      'list',
      'ls',
      'info',
      'view',
      'search',
      'test',
      'run',
      'start',
      'build',
      'dev',
      'lint',
      'format'
    ];

    const parts = command.split(' ');
    if (parts.length < 2) {
      return false;
    }

    const subCommand = parts[1].toLowerCase();
    return safeNpmCommands.includes(subCommand);
  }

  // Check if git command is safe
  isGitCommandSafe(command) {
    const safeGitCommands = [
      'status',
      'log',
      'diff',
      'show',
      'branch',
      'checkout',
      'add',
      'commit',
      'push',
      'pull',
      'fetch',
      'merge',
      'rebase',
      'stash',
      'tag',
      'remote',
      'config'
    ];

    const parts = command.split(' ');
    if (parts.length < 2) {
      return false;
    }

    const subCommand = parts[1].toLowerCase();
    return safeGitCommands.includes(subCommand);
  }

  // Check if an action is safe to execute
  isActionSafe(action) {
    if (!action || !action.type || !action.payload) {
      return false;
    }

    // Rate limiting check
    if (!this.checkRateLimit()) {
      console.warn('Rate limit exceeded');
      return false;
    }

    switch (action.type) {
      case 'CREATE_FILE':
      case 'UPDATE_FILE':
        return this.isFileOperationSafe(action);
      
      case 'DELETE_FILE':
        return this.isDeleteOperationSafe(action);
      
      case 'CREATE_DIRECTORY':
        return this.isPathSafe(action.payload.path);
      
      case 'RUN_COMMAND':
        return this.isCommandSafe(action.payload.command);
      
      default:
        console.warn(`Unknown action type: ${action.type}`);
        return false;
    }
  }

  // Check if file operation is safe
  isFileOperationSafe(action) {
    const { path: filePath, content } = action.payload;
    
    if (!this.isPathSafe(filePath)) {
      return false;
    }

    if (content && content.length > this.maxFileSize) {
      console.warn(`File content too large: ${content.length} bytes`);
      return false;
    }

    // Check for suspicious content
    if (content && this.containsSuspiciousContent(content)) {
      console.warn('Suspicious content detected');
      return false;
    }

    return true;
  }

  // Check if delete operation is safe
  isDeleteOperationSafe(action) {
    const { path: filePath } = action.payload;
    
    if (!this.isPathSafe(filePath)) {
      return false;
    }

    // Extra protection for important files
    const importantFiles = [
      'package.json',
      'package-lock.json',
      'yarn.lock',
      'pnpm-lock.yaml',
      '.gitignore',
      'README.md',
      'LICENSE'
    ];

    const fileName = path.basename(filePath);
    if (importantFiles.includes(fileName)) {
      console.warn(`Attempted to delete important file: ${fileName}`);
      return false;
    }

    return true;
  }

  // Check for suspicious content in files
  containsSuspiciousContent(content) {
    const suspiciousPatterns = [
      /eval\s*\(/i,
      /Function\s*\(/i,
      /document\.write/i,
      /innerHTML\s*=/i,
      /outerHTML\s*=/i,
      /javascript:/i,
      /data:.*base64/i,
      /\<script/i,
      /on\w+\s*=/i, // Event handlers
      /require\s*\(\s*['"]child_process['"]/i,
      /require\s*\(\s*['"]fs['"]/i,
      /process\.env/i,
      /\.env/i
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(content)) {
        return true;
      }
    }

    return false;
  }

  // Rate limiting check
  checkRateLimit() {
    const now = Date.now();
    const windowStart = now - 60000; // 1 minute window
    
    // Clean old entries
    for (const [timestamp] of this.actionCounts) {
      if (timestamp < windowStart) {
        this.actionCounts.delete(timestamp);
      }
    }

    // Count actions in current window
    const actionsInWindow = this.actionCounts.size;
    
    if (actionsInWindow >= this.maxActionsPerMinute) {
      return false;
    }

    // Record this action
    this.actionCounts.set(now, true);
    return true;
  }

  // Validate file extension
  isFileExtensionAllowed(filePath) {
    const allowedExtensions = [
      '.js', '.jsx', '.ts', '.tsx',
      '.json', '.md', '.txt', '.csv',
      '.css', '.scss', '.sass', '.less',
      '.html', '.htm', '.xml',
      '.py', '.java', '.cpp', '.c', '.h',
      '.go', '.rs', '.php', '.rb',
      '.swift', '.kt', '.dart',
      '.sh', '.bash', '.zsh',
      '.yml', '.yaml', '.toml',
      '.sql', '.graphql',
      '.vue', '.svelte'
    ];

    const ext = path.extname(filePath).toLowerCase();
    return !ext || allowedExtensions.includes(ext);
  }

  // Get security report
  getSecurityReport() {
    return {
      projectPath: this.projectPath,
      blockedPatterns: this.blockedPatterns.length,
      blockedDirectories: this.blockedDirectories.length,
      safeCommands: this.safeCommands.length,
      maxFileSize: this.maxFileSize,
      maxActionsPerMinute: this.maxActionsPerMinute,
      currentActionCount: this.actionCounts.size
    };
  }

  // Reset rate limiting (for testing)
  resetRateLimit() {
    this.actionCounts.clear();
  }
}

module.exports = SecurityManager;
