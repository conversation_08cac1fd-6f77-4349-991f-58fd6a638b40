const OpenAI = require('openai');

class OpenAIManager {
  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.model = process.env.OPENAI_MODEL || 'gpt-4';
    this.maxTokens = parseInt(process.env.MAX_TOKENS) || 2000;
    this.temperature = parseFloat(process.env.TEMPERATURE) || 0.7;

    this.systemPrompt = this.buildSystemPrompt();
  }

  buildSystemPrompt() {
    return `You are <PERSON><PERSON><PERSON><PERSON><PERSON>, an AI coding assistant that helps developers with their projects. You have access to the project's file system and can perform various operations.

CAPABILITIES:
- Read and analyze project files
- Create, update, and delete files
- Run safe commands (npm, git, etc.)
- Provide code suggestions and explanations
- Help with debugging and refactoring

RESPONSE FORMAT:
When you need to perform file operations, use this JSON format at the end of your response:

\`\`\`json
{
  "actions": [
    {
      "type": "CREATE_FILE",
      "payload": {
        "path": "src/components/Button.jsx",
        "content": "import React from 'react';\\n\\nconst Button = ({ children, onClick }) => {\\n  return (\\n    <button onClick={onClick}>\\n      {children}\\n    </button>\\n  );\\n};\\n\\nexport default Button;"
      }
    },
    {
      "type": "UPDATE_FILE",
      "payload": {
        "path": "package.json",
        "content": "updated content here"
      }
    },
    {
      "type": "RUN_COMMAND",
      "payload": {
        "command": "npm install react-router-dom"
      }
    }
  ]
}
\`\`\`

AVAILABLE ACTIONS:
- CREATE_FILE: Create a new file
- UPDATE_FILE: Update existing file content
- DELETE_FILE: Delete a file
- CREATE_DIRECTORY: Create a directory
- RUN_COMMAND: Run safe commands (npm, git, node, python, pip, cargo, go)

GUIDELINES:
1. Always explain what you're doing before performing actions
2. Be careful with file operations - ask for confirmation for destructive actions
3. Use relative paths from the project root
4. Follow the project's existing code style and patterns
5. Provide helpful explanations and context
6. If you're unsure about something, ask for clarification

SECURITY:
- Only operate within the project directory
- Don't access sensitive files or system directories
- Validate all file paths and commands
- Don't execute potentially harmful commands

Remember: You're here to help developers be more productive. Be helpful, accurate, and safe.`;
  }

  async getChatCompletion(message, options = {}) {
    const { projectContext, conversationHistory = [] } = options;

    // Build context from project information
    let contextMessage = '';
    if (projectContext) {
      contextMessage = `\n\nCURRENT PROJECT CONTEXT:
Project: ${projectContext.name}
Type: ${projectContext.type}
Path: ${projectContext.path}`;

      if (projectContext.hasPackageJson) {
        contextMessage += `\nPackage.json found with dependencies: ${Object.keys(projectContext.dependencies || {}).join(', ')}`;
        if (projectContext.scripts) {
          contextMessage += `\nAvailable scripts: ${Object.keys(projectContext.scripts).join(', ')}`;
        }
      }
    }

    // Build messages array
    const messages = [
      {
        role: 'system',
        content: this.systemPrompt + contextMessage
      }
    ];

    // Add conversation history
    conversationHistory.forEach(msg => {
      messages.push({
        role: msg.role,
        content: msg.content
      });
    });

    // Add current message
    messages.push({
      role: 'user',
      content: message
    });

    try {
      const stream = await this.client.chat.completions.create({
        model: this.model,
        messages,
        max_tokens: this.maxTokens,
        temperature: this.temperature,
        stream: true,
      });

      return stream;
    } catch (error) {
      console.error('OpenAI API error:', error);
      throw new Error(`AI service error: ${error.message}`);
    }
  }

  async getCodeCompletion(code, language = 'javascript') {
    const prompt = `Complete this ${language} code:\n\n${code}`;
    
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: `You are a code completion assistant. Complete the provided ${language} code snippet. Only return the completed code without explanations.`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3,
      });

      return response.choices[0].message.content.trim();
    } catch (error) {
      console.error('OpenAI code completion error:', error);
      throw new Error(`Code completion error: ${error.message}`);
    }
  }

  async explainCode(code, language = 'javascript') {
    const prompt = `Explain this ${language} code:\n\n${code}`;
    
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are a code explanation assistant. Explain the provided code clearly and concisely.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.5,
      });

      return response.choices[0].message.content.trim();
    } catch (error) {
      console.error('OpenAI code explanation error:', error);
      throw new Error(`Code explanation error: ${error.message}`);
    }
  }

  async generateTests(code, language = 'javascript', framework = 'jest') {
    const prompt = `Generate ${framework} tests for this ${language} code:\n\n${code}`;
    
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: `You are a test generation assistant. Generate comprehensive ${framework} tests for the provided ${language} code. Include edge cases and error scenarios.`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.4,
      });

      return response.choices[0].message.content.trim();
    } catch (error) {
      console.error('OpenAI test generation error:', error);
      throw new Error(`Test generation error: ${error.message}`);
    }
  }

  async refactorCode(code, language = 'javascript', instructions = '') {
    const prompt = `Refactor this ${language} code${instructions ? ` with these instructions: ${instructions}` : ''}:\n\n${code}`;
    
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: `You are a code refactoring assistant. Refactor the provided ${language} code to improve readability, performance, and maintainability. Explain the changes made.`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.3,
      });

      return response.choices[0].message.content.trim();
    } catch (error) {
      console.error('OpenAI refactoring error:', error);
      throw new Error(`Code refactoring error: ${error.message}`);
    }
  }

  // Validate API key and connection
  async validateConnection() {
    try {
      const response = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5,
      });
      
      return { valid: true, model: response.model };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  // Get available models
  async getAvailableModels() {
    try {
      const response = await this.client.models.list();
      return response.data
        .filter(model => model.id.includes('gpt'))
        .map(model => ({
          id: model.id,
          created: model.created,
          owned_by: model.owned_by
        }));
    } catch (error) {
      console.error('Error fetching models:', error);
      return [];
    }
  }
}

module.exports = OpenAIManager;
