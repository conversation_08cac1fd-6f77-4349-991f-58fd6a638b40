# ChatProj API Documentation

This document describes the API endpoints and WebSocket messages for ChatProj.

## REST API Endpoints

### Health Check

**GET** `/api/health`

Check server health and status.

**Response:**
```json
{
  "status": "ok",
  "projectPath": "/path/to/project",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Project Information

**GET** `/api/project-info`

Get information about the current project.

**Response:**
```json
{
  "name": "my-project",
  "version": "1.0.0",
  "description": "My awesome project",
  "type": "node",
  "hasPackageJson": true,
  "dependencies": {
    "express": "^4.18.0"
  },
  "scripts": {
    "start": "node index.js"
  }
}
```

### File Tree

**GET** `/api/file-tree`

Get the project file tree structure.

**Response:**
```json
{
  "name": "project-root",
  "path": ".",
  "type": "directory",
  "children": [
    {
      "name": "src",
      "path": "src",
      "type": "directory",
      "children": [
        {
          "name": "index.js",
          "path": "src/index.js",
          "type": "file",
          "size": 1024,
          "modified": "2024-01-01T00:00:00.000Z",
          "extension": ".js"
        }
      ]
    }
  ]
}
```

### File Operations

**GET** `/api/file/:path`

Read file content.

**Parameters:**
- `path` - File path relative to project root

**Response:**
```json
{
  "content": "file content here",
  "path": "src/index.js"
}
```

**POST** `/api/file/:path`

Create or update file.

**Parameters:**
- `path` - File path relative to project root

**Body:**
```json
{
  "content": "new file content"
}
```

**Response:**
```json
{
  "success": true,
  "path": "src/index.js"
}
```

**DELETE** `/api/file/:path`

Delete file or directory.

**Parameters:**
- `path` - File path relative to project root

**Response:**
```json
{
  "success": true,
  "path": "src/index.js"
}
```

## WebSocket API

### Connection

Connect to WebSocket at the same host and port as the HTTP server.

**URL:** `ws://localhost:3000` or `wss://localhost:3000`

### Message Format

All WebSocket messages use JSON format:

```json
{
  "type": "message-type",
  "payload": {
    // message-specific data
  }
}
```

### Client to Server Messages

#### Chat Message

Send a message to the AI assistant.

```json
{
  "type": "chat-message",
  "payload": {
    "message": "Create a React button component",
    "conversationId": "unique-conversation-id"
  }
}
```

#### File Operation

Request a file operation.

```json
{
  "type": "file-operation",
  "payload": {
    "action": {
      "type": "CREATE_FILE",
      "payload": {
        "path": "src/Button.jsx",
        "content": "import React from 'react';\n\nconst Button = () => {\n  return <button>Click me</button>;\n};\n\nexport default Button;"
      }
    }
  }
}
```

#### Get File Tree

Request the current file tree.

```json
{
  "type": "get-file-tree",
  "payload": {}
}
```

#### Ping

Keep connection alive.

```json
{
  "type": "ping",
  "payload": {}
}
```

### Server to Client Messages

#### Project Info

Project information sent on connection.

```json
{
  "type": "project-info",
  "data": {
    "name": "my-project",
    "type": "node",
    "hasPackageJson": true
  }
}
```

#### File Tree

File tree structure.

```json
{
  "type": "file-tree",
  "data": {
    "name": "project-root",
    "type": "directory",
    "children": []
  }
}
```

#### AI Response Chunk

Streaming AI response.

```json
{
  "type": "ai-response-chunk",
  "conversationId": "conversation-id",
  "content": "I'll create a React button component for you.",
  "isComplete": false,
  "actions": []
}
```

When `isComplete` is `true`, the response is finished and may include actions:

```json
{
  "type": "ai-response-chunk",
  "conversationId": "conversation-id",
  "content": "",
  "isComplete": true,
  "actions": [
    {
      "type": "CREATE_FILE",
      "payload": {
        "path": "src/Button.jsx",
        "content": "component code here"
      }
    }
  ]
}
```

#### Action Result

Result of an executed action.

```json
{
  "type": "action-result",
  "conversationId": "conversation-id",
  "action": {
    "type": "CREATE_FILE",
    "payload": {
      "path": "src/Button.jsx",
      "content": "component code"
    }
  },
  "result": {
    "success": true,
    "path": "src/Button.jsx"
  }
}
```

#### Action Error

Error executing an action.

```json
{
  "type": "action-error",
  "conversationId": "conversation-id",
  "action": {
    "type": "CREATE_FILE",
    "payload": {
      "path": "invalid/path",
      "content": "content"
    }
  },
  "error": "Path outside project directory"
}
```

#### File Change

File system change notification.

```json
{
  "type": "file-change",
  "event": "change",
  "path": "src/index.js"
}
```

#### Error

General error message.

```json
{
  "type": "error",
  "error": "Error message",
  "conversationId": "conversation-id"
}
```

## Action Types

### CREATE_FILE

Create a new file.

```json
{
  "type": "CREATE_FILE",
  "payload": {
    "path": "src/component.js",
    "content": "file content"
  }
}
```

### UPDATE_FILE

Update existing file content.

```json
{
  "type": "UPDATE_FILE",
  "payload": {
    "path": "src/component.js",
    "content": "updated content"
  }
}
```

### DELETE_FILE

Delete a file or directory.

```json
{
  "type": "DELETE_FILE",
  "payload": {
    "path": "src/old-file.js"
  }
}
```

### CREATE_DIRECTORY

Create a directory.

```json
{
  "type": "CREATE_DIRECTORY",
  "payload": {
    "path": "src/components"
  }
}
```

### RUN_COMMAND

Execute a command.

```json
{
  "type": "RUN_COMMAND",
  "payload": {
    "command": "npm install express"
  }
}
```

## Error Handling

### HTTP Errors

- `400 Bad Request` - Invalid request data
- `403 Forbidden` - Access denied (security violation)
- `404 Not Found` - File or endpoint not found
- `500 Internal Server Error` - Server error

### WebSocket Errors

Errors are sent as messages with type `error`:

```json
{
  "type": "error",
  "error": "Error description",
  "code": "ERROR_CODE"
}
```

Common error codes:
- `INVALID_MESSAGE` - Invalid message format
- `SECURITY_VIOLATION` - Security check failed
- `FILE_NOT_FOUND` - Requested file not found
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `AI_SERVICE_ERROR` - OpenAI API error

## Security Considerations

### Path Validation

All file paths are validated to ensure they are within the project directory. Path traversal attempts (`../`) are blocked.

### Command Whitelist

Only whitelisted commands are allowed:
- `npm`, `yarn`, `pnpm`
- `git`
- `node`, `python`, `python3`
- `pip`, `pip3`
- `cargo`, `go`
- `tsc`, `eslint`, `prettier`
- `jest`, `mocha`
- `webpack`, `vite`, `rollup`

### Rate Limiting

- Maximum 30 actions per minute per client
- WebSocket connections are monitored
- Excessive requests result in temporary blocks

### Content Filtering

File content is scanned for suspicious patterns:
- `eval()` and `Function()` calls
- Script injection attempts
- System command execution
- Environment variable access

### File Size Limits

- Maximum file size: 5MB
- Maximum total operation size: 50MB
- Content length validation

## Examples

### Creating a React Component

1. Send chat message:
```json
{
  "type": "chat-message",
  "payload": {
    "message": "Create a React button component with props for text and onClick handler",
    "conversationId": "conv-123"
  }
}
```

2. Receive streaming response and actions:
```json
{
  "type": "ai-response-chunk",
  "conversationId": "conv-123",
  "content": "I'll create a React button component for you.",
  "isComplete": true,
  "actions": [
    {
      "type": "CREATE_FILE",
      "payload": {
        "path": "src/components/Button.jsx",
        "content": "import React from 'react';\n\nconst Button = ({ text, onClick }) => {\n  return (\n    <button onClick={onClick} className=\"btn\">\n      {text}\n    </button>\n  );\n};\n\nexport default Button;"
      }
    }
  ]
}
```

3. Receive action result:
```json
{
  "type": "action-result",
  "conversationId": "conv-123",
  "action": {
    "type": "CREATE_FILE",
    "payload": {
      "path": "src/components/Button.jsx",
      "content": "..."
    }
  },
  "result": {
    "success": true,
    "path": "src/components/Button.jsx"
  }
}
```
