import React from 'react';
import { <PERSON>, ChevronDown, Zap, Brain, Github, <PERSON>rkles } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';

const ModelSelector = ({ providers, selectedModel, onModelChange }) => {
  if (!providers) return null;

  const getProviderIcon = (provider) => {
    switch (provider) {
      case 'openai':
        return <Brain className="w-4 h-4" />;
      case 'gemini':
        return <Sparkles className="w-4 h-4" />;
      case 'openrouter':
        return <Zap className="w-4 h-4" />;
      case 'github':
        return <Github className="w-4 h-4" />;
      default:
        return <Brain className="w-4 h-4" />;
    }
  };

  const getProviderColor = (provider) => {
    switch (provider) {
      case 'openai':
        return 'text-green-500';
      case 'gemini':
        return 'text-blue-500';
      case 'openrouter':
        return 'text-purple-500';
      case 'github':
        return 'text-gray-500';
      default:
        return 'text-gray-500';
    }
  };

  const getModelDisplayName = (model) => {
    // Clean up model names for better display
    if (model.includes('/')) {
      const parts = model.split('/');
      return parts[parts.length - 1];
    }
    return model;
  };

  const isModelFree = (model) => {
    return model.includes(':free') || model.includes('free');
  };

  return (
    <div className="space-y-3">
      {/* Current Provider */}
      <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
        <div className={getProviderColor(providers.provider)}>
          {getProviderIcon(providers.provider)}
        </div>
        <div className="flex-1">
          <div className="font-medium capitalize">{providers.provider}</div>
          <div className="text-xs text-muted-foreground">
            {providers.availableModels.length} models available
          </div>
        </div>
        <Badge variant="outline" className="text-xs">
          Active
        </Badge>
      </div>

      {/* Model Selection */}
      <div>
        <label className="text-sm font-medium mb-2 block">Select Model</label>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-full justify-between">
              <div className="flex items-center gap-2">
                <span className="truncate">
                  {getModelDisplayName(selectedModel || providers.defaultModel)}
                </span>
                {isModelFree(selectedModel || providers.defaultModel) && (
                  <Badge variant="secondary" className="text-xs">
                    Free
                  </Badge>
                )}
              </div>
              <ChevronDown className="w-4 h-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80">
            <DropdownMenuLabel>Available Models</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {providers.availableModels.map((model) => (
              <DropdownMenuItem
                key={model}
                onClick={() => onModelChange(model)}
                className="flex items-center justify-between cursor-pointer"
              >
                <div className="flex items-center gap-2">
                  <span className="truncate">{getModelDisplayName(model)}</span>
                  {isModelFree(model) && (
                    <Badge variant="secondary" className="text-xs">
                      Free
                    </Badge>
                  )}
                </div>
                {selectedModel === model && (
                  <Check className="w-4 h-4 text-primary" />
                )}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Model Info */}
      <div className="text-xs text-muted-foreground space-y-1">
        <div className="flex justify-between">
          <span>Max Tokens:</span>
          <span>{providers.maxTokens.toLocaleString()}</span>
        </div>
        <div className="flex justify-between">
          <span>Temperature:</span>
          <span>{providers.temperature}</span>
        </div>
      </div>

      {/* Provider-specific info */}
      {providers.provider === 'openrouter' && (
        <div className="p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <Zap className="w-3 h-3 text-purple-500" />
            <span className="text-xs font-medium text-purple-700 dark:text-purple-300">
              OpenRouter
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            Access to multiple AI models through a unified API
          </p>
        </div>
      )}

      {providers.provider === 'gemini' && (
        <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <Sparkles className="w-3 h-3 text-blue-500" />
            <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
              Google Gemini
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            Google's advanced AI model with multimodal capabilities
          </p>
        </div>
      )}

      {providers.provider === 'github' && (
        <div className="p-3 bg-gray-500/10 border border-gray-500/20 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <Github className="w-3 h-3 text-gray-500" />
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              GitHub Models
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            AI models hosted on GitHub's infrastructure
          </p>
        </div>
      )}

      {providers.provider === 'openai' && (
        <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <Brain className="w-3 h-3 text-green-500" />
            <span className="text-xs font-medium text-green-700 dark:text-green-300">
              OpenAI
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            Industry-leading AI models from OpenAI
          </p>
        </div>
      )}
    </div>
  );
};

export default ModelSelector;
