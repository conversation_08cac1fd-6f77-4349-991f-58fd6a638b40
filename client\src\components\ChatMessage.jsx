import React from 'react';
import { User, Bot, Info } from 'lucide-react';
import { formatMessage } from '../utils/messageFormatter';

const ChatMessage = ({ message }) => {
  const { type, content, timestamp } = message;

  const getMessageStyle = () => {
    switch (type) {
      case 'user':
        return 'bg-blue-600 text-white ml-12';
      case 'ai':
        return 'bg-gray-700 text-gray-100 mr-12';
      case 'system':
        return 'bg-green-600 text-white mx-auto max-w-md text-center';
      default:
        return 'bg-gray-600 text-gray-100';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'user':
        return <User className="w-5 h-5" />;
      case 'ai':
        return <Bot className="w-5 h-5" />;
      case 'system':
        return <Info className="w-5 h-5" />;
      default:
        return null;
    }
  };

  const formatTimestamp = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(date);
  };

  return (
    <div className={`flex ${type === 'user' ? 'justify-end' : type === 'system' ? 'justify-center' : 'justify-start'}`}>
      <div className={`rounded-lg px-4 py-3 max-w-3xl ${getMessageStyle()}`}>
        {/* Header with icon and timestamp */}
        <div className="flex items-center gap-2 mb-2">
          {getIcon()}
          <span className="text-sm opacity-75">
            {type === 'user' ? 'You' : type === 'ai' ? 'ChatProj' : 'System'}
          </span>
          <span className="text-xs opacity-50 ml-auto">
            {formatTimestamp(timestamp)}
          </span>
        </div>

        {/* Message content */}
        <div 
          className="prose prose-invert max-w-none"
          dangerouslySetInnerHTML={{ __html: formatMessage(content) }}
        />
      </div>
    </div>
  );
};

export default ChatMessage;
