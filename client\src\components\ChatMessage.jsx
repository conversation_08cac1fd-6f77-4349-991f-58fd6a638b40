import * as React from 'react';
import { User, Bot, Info, Clock } from 'lucide-react';
import { formatMessage } from '../utils/messageFormatter';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';

const ChatMessage = ({ message }) => {
  const { type, content, timestamp } = message;

  const getIcon = () => {
    switch (type) {
      case 'user':
        return <User className="w-4 h-4" />;
      case 'ai':
        return <Bot className="w-4 h-4" />;
      case 'system':
        return <Info className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const getMessageAlignment = () => {
    switch (type) {
      case 'user':
        return 'justify-end';
      case 'system':
        return 'justify-center';
      default:
        return 'justify-start';
    }
  };

  const getMessageVariant = () => {
    switch (type) {
      case 'user':
        return 'bg-primary text-primary-foreground';
      case 'ai':
        return 'bg-card border';
      case 'system':
        return 'bg-muted border-muted';
      default:
        return 'bg-card border';
    }
  };

  const formatTimestamp = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(date);
  };

  const getMaxWidth = () => {
    return type === 'system' ? 'max-w-md' : 'max-w-3xl';
  };

  return (
    <div className={`flex ${getMessageAlignment()}`}>
      <Card className={`${getMaxWidth()} ${getMessageVariant()}`}>
        <CardContent className="p-4">
          {/* Header with icon and timestamp */}
          <div className="flex items-center gap-2 mb-3">
            <div className={`p-1.5 rounded-full ${
              type === 'user'
                ? 'bg-primary-foreground/20'
                : type === 'ai'
                ? 'bg-primary/10'
                : 'bg-muted-foreground/10'
            }`}>
              {getIcon()}
            </div>
            <Badge variant={type === 'user' ? 'secondary' : 'outline'} className="text-xs">
              {type === 'user' ? 'You' : type === 'ai' ? 'ChatProj' : 'System'}
            </Badge>
            <div className="flex items-center gap-1 ml-auto text-xs text-muted-foreground">
              <Clock className="w-3 h-3" />
              {formatTimestamp(timestamp)}
            </div>
          </div>

          {/* Message content */}
          <div
            className={`prose prose-sm max-w-none ${
              type === 'user'
                ? 'prose-invert'
                : 'prose-slate dark:prose-invert'
            }`}
            dangerouslySetInnerHTML={{ __html: formatMessage(content) }}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default ChatMessage;
