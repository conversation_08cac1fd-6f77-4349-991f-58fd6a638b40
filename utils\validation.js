const path = require('path');

class ValidationError extends Error {
  constructor(message, field = null) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

// Validate file path
function validateFilePath(filePath, projectPath) {
  if (!filePath || typeof filePath !== 'string') {
    throw new ValidationError('File path is required and must be a string', 'path');
  }

  // Check for path traversal attempts
  if (filePath.includes('..') || filePath.includes('~')) {
    throw new ValidationError('Path traversal not allowed', 'path');
  }

  // Resolve and check if within project directory
  const resolvedPath = path.resolve(projectPath, filePath);
  const resolvedProjectPath = path.resolve(projectPath);
  
  if (!resolvedPath.startsWith(resolvedProjectPath)) {
    throw new ValidationError('Path must be within project directory', 'path');
  }

  return resolvedPath;
}

// Validate file content
function validateFileContent(content, maxSize = 5 * 1024 * 1024) {
  if (content === null || content === undefined) {
    throw new ValidationError('File content is required', 'content');
  }

  if (typeof content !== 'string') {
    throw new ValidationError('File content must be a string', 'content');
  }

  const contentSize = Buffer.byteLength(content, 'utf8');
  if (contentSize > maxSize) {
    throw new ValidationError(`File content too large: ${contentSize} bytes (max: ${maxSize})`, 'content');
  }

  return content;
}

// Validate command
function validateCommand(command, allowedCommands = []) {
  if (!command || typeof command !== 'string') {
    throw new ValidationError('Command is required and must be a string', 'command');
  }

  const trimmedCommand = command.trim();
  if (!trimmedCommand) {
    throw new ValidationError('Command cannot be empty', 'command');
  }

  // Extract base command
  const baseCommand = trimmedCommand.split(' ')[0].toLowerCase();
  
  if (allowedCommands.length > 0 && !allowedCommands.includes(baseCommand)) {
    throw new ValidationError(`Command not allowed: ${baseCommand}`, 'command');
  }

  // Check for dangerous patterns
  const dangerousPatterns = [
    /rm\s+-rf/i,
    /sudo/i,
    /chmod\s+777/i,
    /curl.*\|\s*sh/i,
    /wget.*\|\s*sh/i,
    /eval/i,
    /exec/i
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(trimmedCommand)) {
      throw new ValidationError('Command contains dangerous patterns', 'command');
    }
  }

  return trimmedCommand;
}

// Validate action object
function validateAction(action) {
  if (!action || typeof action !== 'object') {
    throw new ValidationError('Action must be an object', 'action');
  }

  if (!action.type || typeof action.type !== 'string') {
    throw new ValidationError('Action type is required and must be a string', 'type');
  }

  if (!action.payload || typeof action.payload !== 'object') {
    throw new ValidationError('Action payload is required and must be an object', 'payload');
  }

  const validActionTypes = [
    'CREATE_FILE',
    'UPDATE_FILE',
    'DELETE_FILE',
    'CREATE_DIRECTORY',
    'RUN_COMMAND'
  ];

  if (!validActionTypes.includes(action.type)) {
    throw new ValidationError(`Invalid action type: ${action.type}`, 'type');
  }

  // Validate payload based on action type
  switch (action.type) {
    case 'CREATE_FILE':
    case 'UPDATE_FILE':
      if (!action.payload.path) {
        throw new ValidationError('File path is required for file operations', 'payload.path');
      }
      if (action.payload.content === undefined) {
        throw new ValidationError('File content is required for file operations', 'payload.content');
      }
      break;

    case 'DELETE_FILE':
    case 'CREATE_DIRECTORY':
      if (!action.payload.path) {
        throw new ValidationError('Path is required for this operation', 'payload.path');
      }
      break;

    case 'RUN_COMMAND':
      if (!action.payload.command) {
        throw new ValidationError('Command is required for command operations', 'payload.command');
      }
      break;
  }

  return action;
}

// Validate WebSocket message
function validateWebSocketMessage(message) {
  if (!message || typeof message !== 'object') {
    throw new ValidationError('Message must be an object', 'message');
  }

  if (!message.type || typeof message.type !== 'string') {
    throw new ValidationError('Message type is required and must be a string', 'type');
  }

  const validMessageTypes = [
    'chat-message',
    'file-operation',
    'get-file-tree',
    'ping'
  ];

  if (!validMessageTypes.includes(message.type)) {
    throw new ValidationError(`Invalid message type: ${message.type}`, 'type');
  }

  // Validate payload for specific message types
  if (message.type === 'chat-message') {
    if (!message.payload || !message.payload.message) {
      throw new ValidationError('Chat message payload must contain message', 'payload.message');
    }
    if (typeof message.payload.message !== 'string') {
      throw new ValidationError('Chat message must be a string', 'payload.message');
    }
    if (message.payload.message.length > 10000) {
      throw new ValidationError('Chat message too long (max 10000 characters)', 'payload.message');
    }
  }

  return message;
}

// Validate project configuration
function validateProjectConfig(config) {
  if (!config || typeof config !== 'object') {
    throw new ValidationError('Project config must be an object', 'config');
  }

  // Optional validations for known config fields
  if (config.projectName && typeof config.projectName !== 'string') {
    throw new ValidationError('Project name must be a string', 'projectName');
  }

  if (config.settings && typeof config.settings !== 'object') {
    throw new ValidationError('Settings must be an object', 'settings');
  }

  return config;
}

// Sanitize filename
function sanitizeFilename(filename) {
  if (!filename || typeof filename !== 'string') {
    throw new ValidationError('Filename must be a non-empty string', 'filename');
  }

  // Remove or replace dangerous characters
  const sanitized = filename
    .replace(/[<>:"/\\|?*]/g, '_') // Replace dangerous chars with underscore
    .replace(/\.\./g, '_') // Replace .. with underscore
    .replace(/^\.+/, '') // Remove leading dots
    .trim();

  if (!sanitized) {
    throw new ValidationError('Filename cannot be empty after sanitization', 'filename');
  }

  if (sanitized.length > 255) {
    throw new ValidationError('Filename too long (max 255 characters)', 'filename');
  }

  return sanitized;
}

// Rate limiting validation
function validateRateLimit(clientId, actionCounts, maxActionsPerMinute = 30) {
  const now = Date.now();
  const windowStart = now - 60000; // 1 minute window

  if (!actionCounts.has(clientId)) {
    actionCounts.set(clientId, []);
  }

  const clientActions = actionCounts.get(clientId);
  
  // Remove old actions outside the window
  const recentActions = clientActions.filter(timestamp => timestamp > windowStart);
  actionCounts.set(clientId, recentActions);

  if (recentActions.length >= maxActionsPerMinute) {
    throw new ValidationError(`Rate limit exceeded: ${recentActions.length} actions in the last minute`, 'rateLimit');
  }

  // Record this action
  recentActions.push(now);
  
  return true;
}

module.exports = {
  ValidationError,
  validateFilePath,
  validateFileContent,
  validateCommand,
  validateAction,
  validateWebSocketMessage,
  validateProjectConfig,
  sanitizeFilename,
  validateRateLimit
};
