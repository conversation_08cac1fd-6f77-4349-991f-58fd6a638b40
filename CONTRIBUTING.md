# Contributing to <PERSON><PERSON><PERSON><PERSON><PERSON>

Thank you for your interest in contributing to ChatProj! This document provides guidelines and information for contributors.

## Development Setup

### Prerequisites

- Node.js 14+ 
- npm or yarn
- OpenAI API key

### Getting Started

1. **Clone the repository**
   ```bash
   git clone https://github.com/chatproj/chatproj.git
   cd chatproj
   ```

2. **Install dependencies**
   ```bash
   npm install
   cd client && npm install && cd ..
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env and add your OPENAI_API_KEY
   ```

4. **Run tests**
   ```bash
   npm test
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

## Project Structure

```
chatproj/
├── cli.js              # CLI entry point
├── server.js           # Main server
├── lib/                # Core libraries
│   ├── fileSystemManager.js
│   ├── openaiManager.js
│   ├── actionParser.js
│   └── securityManager.js
├── utils/              # Utility functions
├── client/             # React frontend
│   ├── src/
│   └── dist/
├── tests/              # Test files
└── public/             # Static assets
```

## Development Guidelines

### Code Style

- Use ESLint configuration provided
- Follow existing code patterns
- Use meaningful variable and function names
- Add JSDoc comments for public APIs

### Testing

- Write tests for new features
- Maintain test coverage above 80%
- Use descriptive test names
- Test both success and error cases

### Security

- All file operations must be validated
- Commands must be whitelisted
- User input must be sanitized
- Rate limiting must be enforced

## Contributing Process

### 1. Issue First

- Check existing issues before creating new ones
- Use issue templates when available
- Provide detailed reproduction steps for bugs
- Include system information and versions

### 2. Fork and Branch

```bash
git checkout -b feature/your-feature-name
# or
git checkout -b fix/issue-number
```

### 3. Make Changes

- Follow the coding standards
- Write or update tests
- Update documentation if needed
- Ensure all tests pass

### 4. Commit Messages

Use conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test changes
- `chore`: Build/tooling changes

Examples:
```
feat(parser): add support for TypeScript files
fix(security): prevent path traversal in file operations
docs(readme): update installation instructions
```

### 5. Pull Request

- Use the PR template
- Link related issues
- Provide clear description of changes
- Include screenshots for UI changes
- Ensure CI passes

## Areas for Contribution

### High Priority

- **Language Support**: Add support for more programming languages
- **IDE Integration**: VS Code extension, Vim plugin, etc.
- **Performance**: Optimize file operations and AI responses
- **Testing**: Increase test coverage and add integration tests

### Medium Priority

- **UI/UX**: Improve the web interface
- **Documentation**: API docs, tutorials, examples
- **Error Handling**: Better error messages and recovery
- **Logging**: Structured logging and debugging tools

### Low Priority

- **Themes**: Dark/light mode, custom themes
- **Plugins**: Plugin system for extensions
- **Analytics**: Usage analytics and metrics
- **Deployment**: Docker, cloud deployment guides

## API Guidelines

### File System Operations

```javascript
// Good
const result = await fileManager.writeFile('src/component.js', content);
if (result.success) {
  // Handle success
}

// Bad - no error handling
fileManager.writeFile('src/component.js', content);
```

### Security Validation

```javascript
// Always validate paths
if (!securityManager.isPathSafe(filePath)) {
  throw new Error('Invalid file path');
}

// Always validate actions
if (!securityManager.isActionSafe(action)) {
  throw new Error('Action not permitted');
}
```

### WebSocket Messages

```javascript
// Use consistent message format
const message = {
  type: 'action-type',
  payload: {
    // action-specific data
  },
  timestamp: new Date().toISOString()
};
```

## Testing Guidelines

### Unit Tests

- Test individual functions and classes
- Mock external dependencies
- Use descriptive test names
- Test edge cases and error conditions

```javascript
describe('FileSystemManager', () => {
  describe('writeFile', () => {
    test('should create file with valid content', () => {
      // Test implementation
    });

    test('should throw error for invalid path', () => {
      // Test implementation
    });
  });
});
```

### Integration Tests

- Test complete workflows
- Use temporary directories
- Clean up after tests
- Test WebSocket communication

## Documentation

### Code Documentation

- Use JSDoc for public APIs
- Include parameter types and descriptions
- Provide usage examples
- Document error conditions

```javascript
/**
 * Write content to a file
 * @param {string} filePath - Relative path to file
 * @param {string} content - File content
 * @returns {Promise<{success: boolean, path: string}>} Write result
 * @throws {Error} If path is invalid or content too large
 */
async writeFile(filePath, content) {
  // Implementation
}
```

### User Documentation

- Keep README up to date
- Provide clear examples
- Document configuration options
- Include troubleshooting guides

## Release Process

1. Update version in package.json
2. Update CHANGELOG.md
3. Create release branch
4. Run full test suite
5. Create GitHub release
6. Publish to npm

## Getting Help

- Join our Discord server
- Check existing issues and discussions
- Ask questions in GitHub Discussions
- Email maintainers for security issues

## Code of Conduct

- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Follow GitHub's community guidelines

Thank you for contributing to ChatProj!
