# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Server Configuration
PORT=3000
HOST=localhost

# Development Settings
NODE_ENV=development

# Security Settings
ALLOWED_EXTENSIONS=.js,.jsx,.ts,.tsx,.json,.md,.txt,.css,.scss,.html,.vue,.py,.java,.cpp,.c,.h,.go,.rs,.php,.rb,.swift,.kt,.dart,.sh,.yml,.yaml,.xml,.sql

# AI Model Configuration
OPENAI_MODEL=gpt-4
MAX_TOKENS=2000
TEMPERATURE=0.7

# File System Settings
MAX_FILE_SIZE=1048576
MAX_FILES_PER_OPERATION=10
