const fs = require('fs');
const path = require('path');
const os = require('os');
const FileSystemManager = require('../lib/fileSystemManager');

describe('FileSystemManager', () => {
  let tempDir;
  let fsManager;

  beforeEach(() => {
    // Create temporary directory for testing
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'chatproj-test-'));
    fsManager = new FileSystemManager(tempDir);
  });

  afterEach(() => {
    // Clean up temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('getProjectInfo', () => {
    test('should return basic project info for directory without package.json', () => {
      const info = fsManager.getProjectInfo();
      
      expect(info).toMatchObject({
        name: expect.any(String),
        path: tempDir,
        type: 'unknown',
        hasPackageJson: false
      });
    });

    test('should return detailed info for Node.js project', () => {
      const packageJson = {
        name: 'test-project',
        version: '1.0.0',
        description: 'Test project',
        dependencies: {
          'express': '^4.18.0'
        },
        scripts: {
          'start': 'node index.js'
        }
      };

      fs.writeFileSync(
        path.join(tempDir, 'package.json'),
        JSON.stringify(packageJson, null, 2)
      );

      const info = fsManager.getProjectInfo();
      
      expect(info).toMatchObject({
        name: 'test-project',
        version: '1.0.0',
        description: 'Test project',
        type: 'node',
        hasPackageJson: true,
        dependencies: { 'express': '^4.18.0' },
        scripts: { 'start': 'node index.js' }
      });
    });
  });

  describe('writeFile and readFile', () => {
    test('should write and read file successfully', () => {
      const filePath = 'test.txt';
      const content = 'Hello, World!';

      const writeResult = fsManager.writeFile(filePath, content);
      expect(writeResult).toEqual({ success: true, path: filePath });

      const readContent = fsManager.readFile(filePath);
      expect(readContent).toBe(content);
    });

    test('should create directories if they do not exist', () => {
      const filePath = 'nested/dir/test.txt';
      const content = 'Nested file content';

      fsManager.writeFile(filePath, content);
      
      const readContent = fsManager.readFile(filePath);
      expect(readContent).toBe(content);
    });

    test('should throw error for file outside project directory', () => {
      expect(() => {
        fsManager.writeFile('../outside.txt', 'content');
      }).toThrow('Access denied: Path outside project directory');
    });

    test('should throw error for non-existent file', () => {
      expect(() => {
        fsManager.readFile('nonexistent.txt');
      }).toThrow('File not found: nonexistent.txt');
    });
  });

  describe('deleteFile', () => {
    test('should delete file successfully', () => {
      const filePath = 'delete-me.txt';
      fsManager.writeFile(filePath, 'content');
      
      const deleteResult = fsManager.deleteFile(filePath);
      expect(deleteResult).toEqual({ success: true, path: filePath });
      
      expect(() => {
        fsManager.readFile(filePath);
      }).toThrow('File not found');
    });

    test('should delete directory recursively', () => {
      const dirPath = 'delete-dir';
      fsManager.createDirectory(dirPath);
      fsManager.writeFile(path.join(dirPath, 'file.txt'), 'content');
      
      const deleteResult = fsManager.deleteFile(dirPath);
      expect(deleteResult).toEqual({ success: true, path: dirPath });
      
      expect(fs.existsSync(path.join(tempDir, dirPath))).toBe(false);
    });
  });

  describe('createDirectory', () => {
    test('should create directory successfully', () => {
      const dirPath = 'new-directory';
      
      const result = fsManager.createDirectory(dirPath);
      expect(result).toEqual({ success: true, path: dirPath });
      
      expect(fs.existsSync(path.join(tempDir, dirPath))).toBe(true);
    });

    test('should create nested directories', () => {
      const dirPath = 'nested/deep/directory';
      
      fsManager.createDirectory(dirPath);
      
      expect(fs.existsSync(path.join(tempDir, dirPath))).toBe(true);
    });
  });

  describe('getFileTree', () => {
    test('should return file tree structure', () => {
      // Create test structure
      fsManager.createDirectory('src');
      fsManager.writeFile('src/index.js', 'console.log("hello");');
      fsManager.writeFile('README.md', '# Test Project');
      fsManager.writeFile('package.json', '{}');

      const tree = fsManager.getFileTree();
      
      expect(tree).toMatchObject({
        name: expect.any(String),
        type: 'directory',
        children: expect.arrayContaining([
          expect.objectContaining({
            name: 'src',
            type: 'directory',
            children: expect.arrayContaining([
              expect.objectContaining({
                name: 'index.js',
                type: 'file'
              })
            ])
          }),
          expect.objectContaining({
            name: 'README.md',
            type: 'file'
          }),
          expect.objectContaining({
            name: 'package.json',
            type: 'file'
          })
        ])
      });
    });
  });

  describe('runCommand', () => {
    test('should run safe command successfully', async () => {
      const result = await fsManager.runCommand('node --version');
      
      expect(result).toMatchObject({
        success: true,
        command: 'node --version',
        stdout: expect.stringMatching(/v\d+\.\d+\.\d+/)
      });
    });

    test('should reject unsafe command', async () => {
      const result = await fsManager.runCommand('rm -rf /');
      
      expect(result).toMatchObject({
        success: false,
        command: 'rm -rf /',
        error: expect.stringContaining('Command not allowed')
      });
    });
  });

  describe('searchFiles', () => {
    test('should find files by pattern', () => {
      fsManager.writeFile('test.js', 'content');
      fsManager.writeFile('test.txt', 'content');
      fsManager.writeFile('other.md', 'content');

      const results = fsManager.searchFiles('test');
      
      expect(results).toHaveLength(2);
      expect(results.map(r => r.name)).toEqual(
        expect.arrayContaining(['test.js', 'test.txt'])
      );
    });
  });

  describe('validateFileExtension', () => {
    test('should allow common file extensions', () => {
      expect(() => {
        fsManager.validateFileExtension('test.js');
      }).not.toThrow();

      expect(() => {
        fsManager.validateFileExtension('test.json');
      }).not.toThrow();
    });

    test('should reject dangerous file extensions', () => {
      // Mock the allowed extensions to exclude .exe
      fsManager.allowedExtensions = ['.js', '.json'];
      
      expect(() => {
        fsManager.validateFileExtension('malware.exe');
      }).toThrow('File extension not allowed');
    });
  });
});
