// Jest setup file for global test configuration

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.OPENAI_API_KEY = 'test-api-key';
process.env.LOG_LEVEL = 'error';

// Global test utilities
global.testUtils = {
  // Helper to create temporary directories
  createTempDir: () => {
    const fs = require('fs');
    const path = require('path');
    const os = require('os');
    return fs.mkdtempSync(path.join(os.tmpdir(), 'chatproj-test-'));
  },

  // Helper to clean up temporary directories
  cleanupTempDir: (dir) => {
    const fs = require('fs');
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
    }
  },

  // Helper to create mock WebSocket
  createMockWebSocket: () => ({
    send: jest.fn(),
    close: jest.fn(),
    readyState: 1, // OPEN
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  }),

  // Helper to create mock file system structure
  createMockFileStructure: (fsManager, structure) => {
    for (const [path, content] of Object.entries(structure)) {
      if (typeof content === 'string') {
        fsManager.writeFile(path, content);
      } else if (content === null) {
        fsManager.createDirectory(path);
      }
    }
  }
};

// Increase timeout for integration tests
jest.setTimeout(30000);
