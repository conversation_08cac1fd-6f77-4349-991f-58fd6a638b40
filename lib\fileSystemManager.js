const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const sanitizeFilename = require('sanitize-filename');

const execAsync = promisify(exec);

class FileSystemManager {
  constructor(projectPath) {
    this.projectPath = path.resolve(projectPath);
    this.maxFileSize = parseInt(process.env.MAX_FILE_SIZE) || 1048576; // 1MB default
    this.allowedExtensions = (process.env.ALLOWED_EXTENSIONS || 
      '.js,.jsx,.ts,.tsx,.json,.md,.txt,.css,.scss,.html,.vue,.py,.java,.cpp,.c,.h,.go,.rs,.php,.rb,.swift,.kt,.dart,.sh,.yml,.yaml,.xml,.sql')
      .split(',');
  }

  // Get project information
  getProjectInfo() {
    const packageJsonPath = path.join(this.projectPath, 'package.json');
    let projectInfo = {
      name: path.basename(this.projectPath),
      path: this.projectPath,
      type: 'unknown',
      hasPackageJson: false
    };

    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        projectInfo = {
          ...projectInfo,
          name: packageJson.name || projectInfo.name,
          version: packageJson.version,
          description: packageJson.description,
          type: 'node',
          hasPackageJson: true,
          dependencies: packageJson.dependencies || {},
          devDependencies: packageJson.devDependencies || {},
          scripts: packageJson.scripts || {}
        };
      } catch (error) {
        console.warn('Error reading package.json:', error.message);
      }
    }

    // Detect other project types
    if (fs.existsSync(path.join(this.projectPath, 'requirements.txt'))) {
      projectInfo.type = 'python';
    } else if (fs.existsSync(path.join(this.projectPath, 'Cargo.toml'))) {
      projectInfo.type = 'rust';
    } else if (fs.existsSync(path.join(this.projectPath, 'go.mod'))) {
      projectInfo.type = 'go';
    }

    return projectInfo;
  }

  // Get file tree structure
  getFileTree(dirPath = this.projectPath, maxDepth = 3, currentDepth = 0) {
    if (currentDepth >= maxDepth) return null;

    const relativePath = path.relative(this.projectPath, dirPath);
    const name = path.basename(dirPath);

    // Skip hidden directories and common ignore patterns
    const skipPatterns = [
      'node_modules', '.git', '.chatproj', 'dist', 'build', 
      '.next', '.nuxt', 'coverage', '.nyc_output', '__pycache__'
    ];
    
    if (skipPatterns.includes(name) || name.startsWith('.')) {
      return null;
    }

    try {
      const stats = fs.statSync(dirPath);
      
      if (stats.isDirectory()) {
        const children = [];
        const entries = fs.readdirSync(dirPath);
        
        for (const entry of entries) {
          const childPath = path.join(dirPath, entry);
          const childNode = this.getFileTree(childPath, maxDepth, currentDepth + 1);
          if (childNode) {
            children.push(childNode);
          }
        }

        return {
          name,
          path: relativePath || '.',
          type: 'directory',
          children: children.sort((a, b) => {
            // Directories first, then files
            if (a.type !== b.type) {
              return a.type === 'directory' ? -1 : 1;
            }
            return a.name.localeCompare(b.name);
          })
        };
      } else {
        return {
          name,
          path: relativePath,
          type: 'file',
          size: stats.size,
          modified: stats.mtime.toISOString(),
          extension: path.extname(name)
        };
      }
    } catch (error) {
      console.warn(`Error reading ${dirPath}:`, error.message);
      return null;
    }
  }

  // Read file content
  readFile(relativePath) {
    const fullPath = this.getFullPath(relativePath);
    this.validatePath(fullPath);
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${relativePath}`);
    }

    const stats = fs.statSync(fullPath);
    if (stats.size > this.maxFileSize) {
      throw new Error(`File too large: ${relativePath} (${stats.size} bytes)`);
    }

    return fs.readFileSync(fullPath, 'utf8');
  }

  // Write file content
  writeFile(relativePath, content) {
    const fullPath = this.getFullPath(relativePath);
    this.validatePath(fullPath);
    this.validateFileExtension(relativePath);

    if (Buffer.byteLength(content, 'utf8') > this.maxFileSize) {
      throw new Error(`Content too large for file: ${relativePath}`);
    }

    // Ensure directory exists
    const dir = path.dirname(fullPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(fullPath, content, 'utf8');
    return { success: true, path: relativePath };
  }

  // Delete file
  deleteFile(relativePath) {
    const fullPath = this.getFullPath(relativePath);
    this.validatePath(fullPath);

    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${relativePath}`);
    }

    const stats = fs.statSync(fullPath);
    if (stats.isDirectory()) {
      fs.rmSync(fullPath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(fullPath);
    }

    return { success: true, path: relativePath };
  }

  // Create directory
  createDirectory(relativePath) {
    const fullPath = this.getFullPath(relativePath);
    this.validatePath(fullPath);

    fs.mkdirSync(fullPath, { recursive: true });
    return { success: true, path: relativePath };
  }

  // Run command in project directory
  async runCommand(command) {
    // Sanitize command - only allow safe commands
    const safeCommands = ['npm', 'yarn', 'pnpm', 'git', 'node', 'python', 'pip', 'cargo', 'go'];
    const commandParts = command.trim().split(' ');
    const baseCommand = commandParts[0];

    if (!safeCommands.includes(baseCommand)) {
      throw new Error(`Command not allowed: ${baseCommand}`);
    }

    try {
      const { stdout, stderr } = await execAsync(command, {
        cwd: this.projectPath,
        timeout: 30000, // 30 second timeout
        maxBuffer: 1024 * 1024 // 1MB buffer
      });

      return {
        success: true,
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        command
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        stdout: error.stdout || '',
        stderr: error.stderr || '',
        command
      };
    }
  }

  // Helper methods
  getFullPath(relativePath) {
    // Sanitize the path
    const sanitized = relativePath.split(path.sep).map(part => sanitizeFilename(part)).join(path.sep);
    return path.resolve(this.projectPath, sanitized);
  }

  validatePath(fullPath) {
    // Ensure path is within project directory
    const resolvedPath = path.resolve(fullPath);
    const resolvedProjectPath = path.resolve(this.projectPath);
    
    if (!resolvedPath.startsWith(resolvedProjectPath)) {
      throw new Error('Access denied: Path outside project directory');
    }
  }

  validateFileExtension(relativePath) {
    const ext = path.extname(relativePath).toLowerCase();
    if (ext && !this.allowedExtensions.includes(ext)) {
      throw new Error(`File extension not allowed: ${ext}`);
    }
  }

  // Get file stats
  getFileStats(relativePath) {
    const fullPath = this.getFullPath(relativePath);
    this.validatePath(fullPath);

    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${relativePath}`);
    }

    const stats = fs.statSync(fullPath);
    return {
      size: stats.size,
      created: stats.birthtime.toISOString(),
      modified: stats.mtime.toISOString(),
      isDirectory: stats.isDirectory(),
      isFile: stats.isFile()
    };
  }

  // Search files by pattern
  searchFiles(pattern, maxResults = 100) {
    const results = [];
    
    const searchDir = (dirPath, currentDepth = 0) => {
      if (currentDepth > 5 || results.length >= maxResults) return;
      
      try {
        const entries = fs.readdirSync(dirPath);
        
        for (const entry of entries) {
          if (results.length >= maxResults) break;
          
          const fullPath = path.join(dirPath, entry);
          const relativePath = path.relative(this.projectPath, fullPath);
          
          // Skip hidden and ignored directories
          if (entry.startsWith('.') || entry === 'node_modules') continue;
          
          const stats = fs.statSync(fullPath);
          
          if (stats.isDirectory()) {
            searchDir(fullPath, currentDepth + 1);
          } else if (entry.toLowerCase().includes(pattern.toLowerCase())) {
            results.push({
              path: relativePath,
              name: entry,
              size: stats.size,
              modified: stats.mtime.toISOString()
            });
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    };

    searchDir(this.projectPath);
    return results;
  }
}

module.exports = FileSystemManager;
