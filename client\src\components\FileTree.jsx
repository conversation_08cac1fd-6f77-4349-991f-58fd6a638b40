import React, { useState } from 'react';
import { 
  <PERSON>older, 
  Folder<PERSON><PERSON>, 
  FileText, 
  Code, 
  Image, 
  FileJson,
  Settings,
  ChevronRight,
  ChevronDown
} from 'lucide-react';

const FileTree = ({ tree, onFileSelect, selectedFile }) => {
  const [expandedFolders, setExpandedFolders] = useState(new Set([''])); // Root is expanded by default

  const toggleFolder = (path) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  const getFileIcon = (file) => {
    if (file.type === 'directory') {
      return expandedFolders.has(file.path) ? 
        <FolderOpen className="w-4 h-4 text-blue-400" /> : 
        <Folder className="w-4 h-4 text-blue-400" />;
    }

    const extension = file.extension?.toLowerCase();
    
    switch (extension) {
      case '.js':
      case '.jsx':
      case '.ts':
      case '.tsx':
      case '.vue':
      case '.svelte':
        return <Code className="w-4 h-4 text-yellow-400" />;
      case '.json':
        return <FileJson className="w-4 h-4 text-green-400" />;
      case '.png':
      case '.jpg':
      case '.jpeg':
      case '.gif':
      case '.svg':
      case '.webp':
        return <Image className="w-4 h-4 text-purple-400" />;
      case '.md':
      case '.txt':
        return <FileText className="w-4 h-4 text-gray-400" />;
      case '.env':
      case '.gitignore':
      case '.eslintrc':
      case '.prettierrc':
        return <Settings className="w-4 h-4 text-orange-400" />;
      default:
        return <FileText className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderTreeNode = (node, depth = 0) => {
    if (!node) return null;

    const isExpanded = expandedFolders.has(node.path);
    const isSelected = selectedFile?.path === node.path;
    const isDirectory = node.type === 'directory';

    return (
      <div key={node.path}>
        <div
          className={`
            flex items-center gap-2 py-1 px-2 rounded cursor-pointer
            hover:bg-gray-700 transition-colors
            ${isSelected ? 'bg-blue-600 text-white' : ''}
          `}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => {
            if (isDirectory) {
              toggleFolder(node.path);
            } else {
              onFileSelect?.(node);
            }
          }}
        >
          {/* Expand/collapse chevron for directories */}
          {isDirectory && (
            <div className="w-4 h-4 flex items-center justify-center">
              {isExpanded ? (
                <ChevronDown className="w-3 h-3 text-gray-400" />
              ) : (
                <ChevronRight className="w-3 h-3 text-gray-400" />
              )}
            </div>
          )}
          
          {/* File/folder icon */}
          {getFileIcon(node)}
          
          {/* File/folder name */}
          <span className="text-sm truncate flex-1">
            {node.name}
          </span>
          
          {/* File size for files */}
          {!isDirectory && node.size && (
            <span className="text-xs text-gray-500">
              {formatFileSize(node.size)}
            </span>
          )}
        </div>

        {/* Render children if directory is expanded */}
        {isDirectory && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderTreeNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (!tree) {
    return (
      <div className="text-sm text-gray-500 p-2">
        No files found
      </div>
    );
  }

  return (
    <div className="text-sm">
      {renderTreeNode(tree)}
    </div>
  );
};

export default FileTree;
